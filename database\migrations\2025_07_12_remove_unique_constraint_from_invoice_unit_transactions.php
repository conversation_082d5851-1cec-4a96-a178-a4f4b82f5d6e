<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the index exists before trying to drop it
        try {
            Schema::table('invoice_unit_transactions', function (Blueprint $table) {
                // Remove the unique constraint on unit_transaction_id
                $table->dropUnique(['unit_transaction_id']);
            });
        } catch (\Exception $e) {
            // Log the error but continue with the migration
            \Illuminate\Support\Facades\Log::info('Error dropping unique constraint: ' . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_unit_transactions', function (Blueprint $table) {
            // Add the unique constraint back
            $table->unique('unit_transaction_id');
        });
    }
};
