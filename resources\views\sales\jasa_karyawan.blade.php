@extends('sales.contentsales')
@section('title', 'Monthly Report')
@section('resourcesales')
@vite(['resources/js/sales/jasa_karyawan.js'])
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px;
    }
    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
        font-size: 11px;
        text-transform: uppercase;
    }
    .status-submitted {
        background-color: #f8f9fa;
        color: #6c757d;
        border: 1px solid #6c757d;
    }
    .status-approved {
        background-color: #d1e7dd;
        color: #0f5132;
        border: 1px solid #0f5132;
    }
    .status-rejected {
        background-color: #f8d7da;
        color: #842029;
        border: 1px solid #842029;
    }
    .status-done {
        background-color: #cff4fc;
        color: #055160;
        border: 1px solid #055160;
    }
</style>
@endsection
@section('contentsales')

@include('sales.partials.navigation')

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Konfirmasi Monthly Report</h5>
                        </div>
                        <div class="d-flex">
                            <input type="date" id="date-from" class="form-control form-control-sm mr-2" value="{{ now()->subMonth()->format('Y-m-d') }}">
                            <input type="date" id="date-to" class="form-control form-control-sm mr-2" value="{{ now()->format('Y-m-d') }}">
                            <select id="site-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Site</option>
                                @foreach($sites as $site)
                                <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                @endforeach
                            </select>
                            <select id="status-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Status</option>
                                <option value="submitted">Diajukan</option>
                                <option value="approved">Disetujui</option>
                                <option value="rejected">Ditolak</option>
                                <option value="done">Selesai</option>
                            </select>
                            <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Search...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="jasa-karyawan-table" style="font-size: 11px;">
                                <thead class="bg-light">
                                    <tr>
                                        <th>No</th>
                                        <th>Site</th>
                                        <th>Karyawan</th>
                                        <th>Tanggal</th>
                                        <th>Jumlah</th>
                                        <th>Status</th>
                                        <th>Catatan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="jasa-karyawan-table-body">
                                    <!-- Data will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="entries-info">Showing 0 to 0 of 0 entries</span>
                            </div>
                            <div id="pagination-container">
                                <!-- Pagination will be loaded dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Monthly ReportDetail Modal -->
<div class="modal fade" id="jasa-karyawan-detail-modal" tabindex="-1" role="dialog" aria-labelledby="jasa-karyawan-detail-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document" style="max-width: 90%;">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <h5 class="modal-title text-white font-bold" id="jasa-karyawan-detail-modal-label">Detail Monthly Report</h5>
                    <div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Left Column:Monthly ReportDetails -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 font-bold">Informasi Monthly Report</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-bordered">
                                    <tr>
                                        <th class="bg-light">ID</th>
                                        <td id="detail-id"></td>
                                    </tr>
                                    <tr>
                                        <th class="bg-light">Site</th>
                                        <td id="detail-site"></td>
                                    </tr>
                                    <tr>
                                        <th class="bg-light">Karyawan</th>
                                        <td id="detail-employee"></td>
                                    </tr>
                                    <tr>
                                        <th class="bg-light">Tanggal</th>
                                        <td id="detail-date"></td>
                                    </tr>
                                    <tr>
                                        <th class="bg-light">Jumlah</th>
                                        <td id="detail-amount"></td>
                                    </tr>
                                    <tr>
                                        <th class="bg-light">Status</th>
                                        <td id="detail-status"></td>
                                    </tr>
                                    <tr>
                                        <th class="bg-light">Catatan</th>
                                        <td id="detail-notes"></td>
                                    </tr>
                                    <tr>
                                        <th class="bg-light">Dibuat Pada</th>
                                        <td id="detail-created-at"></td>
                                    </tr>
                                </table>

                                <!-- Status Update Form -->
                                <div id="status-update-container" class="mt-3">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h5 class="mb-0 font-bold">Update Status</h5>
                                        </div>
                                        <div class="card-body">
                                            <form id="update-status-form">
                                                <input type="hidden" id="jasa-karyawan-id">
                                                <div class="form-group mb-3">
                                                    <label for="status" class="form-label">Status</label>
                                                    <select id="status" name="status" class="form-select">
                                                        <option value="approved">Disetujui</option>
                                                        <option value="rejected">Ditolak</option>
                                                        <option value="done">Selesai</option>
                                                    </select>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="notes" class="form-label">Catatan</label>
                                                    <textarea id="notes" name="notes" class="form-control" rows="4" placeholder="Tambahkan catatan untukMonthly Reportini..."></textarea>
                                                </div>
                                                <div class="d-grid">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="mdi mdi-content-save"></i> Simpan Perubahan
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column: File Preview -->
                    <div class="col-md-9">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 font-bold">Dokumen</h5>
                            </div>
                            <div class="card-body">
                                <div id="file-container" class="text-center">
                                    <!-- File will be loaded here -->
                                    <div id="no-file-message" class="alert alert-info d-none">
                                        Tidak ada dokumen untukMonthly Reportini.
                                    </div>
                                    <div id="file-preview" class="mb-3" style="min-height: 600px; max-height: 800px; overflow: auto;">
                                        <!-- Preview will be shown here -->
                                    </div>
                                    <div id="file-actions" class="d-none">
                                        <a id="download-file" href="#" class="btn btn-sm btn-primary" target="_blank">
                                            <i class="mdi mdi-download"></i> Download Dokumen
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>
@endsection
