
<?php $__env->startSection('contentho'); ?>
<div class="row bgwhite page-title-box shadow-kit mb-1 rounded-lg">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0"><?php echo e(session('name')); ?></p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="row pt-2">
    <div class="col bgwhite shadow-kit p-4 rounded-lg">
        <h4 class="text-uppercase h4 font-bold">Tabel part masing-masing Site</h4>

        <div class="d-flex justify-content-between mb-3">
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    <label for="siteFilter" class="form-label mr-2">Filter Site:</label>
                    <select id="siteFilter" class="btn btn-primary btn-sm">
                        <option value="">Semua Site</option>
                        <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($site->site_id); ?>" <?php echo e($selectedSite == $site->site_id ? 'selected' : ''); ?>><?php echo e($site->site_name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div>
                    <label for="typeFilter" class="form-label mr-2">Tipe Part:</label>
                    <select id="typeFilter" class="btn btn-primary btn-sm">
                        <option value="">Semua Tipe</option>
                        <option value="AC" <?php echo e($partType == 'AC' ? 'selected' : ''); ?>>AC</option>
                        <option value="TYRE" <?php echo e($partType == 'TYRE' ? 'selected' : ''); ?>>TYRE</option>
                        <option value="FABRIKASI" <?php echo e($partType == 'FABRIKASI' ? 'selected' : ''); ?>>FABRIKASI</option>
                        <option value="PERLENGKAPAN AC" <?php echo e($partType == 'PERLENGKAPAN AC' ? 'selected' : ''); ?>>PERLENGKAPAN AC</option>
                        <option value="PERSEDIAAN LAINNYA" <?php echo e($partType == 'PERSEDIAAN LAINNYA' ? 'selected' : ''); ?>>PERSEDIAAN LAINNYA</option>
                    </select>
                </div>
            </div>

            <div class="input-group max400">
                <input type="text" id="searchInput" class="form-control" placeholder="Cari Part Code atau Nama..." value="<?php echo e($search ?? ''); ?>">
            </div>
        </div>

        <table class="table table-bordered table-hover">
            <thead class="table-dark text-white">
                <tr>
                    <th class="p-2">Part Code</th>
                    <th class="p-2">Part Name</th>
                    <th class="p-2">Site Part Name</th>
                    <th class="p-2">Bin Location</th>
                    <th class="p-2">Site</th>
                    <th class="p-2">Min Stock</th>
                    <th class="p-2">Max Stock</th>
                    <th class="p-2">Stock</th>
                    <th class="p-2">Aksi</th>
                </tr>
            </thead>
            <tbody id="partTableBody">
                <?php $__empty_1 = true; $__currentLoopData = $parts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partInventory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($partInventory->part->part_code); ?></td>
                    <td><?php echo e($partInventory->part->part_name); ?></td>
                    <td><?php echo e($partInventory->site_part_name ?? $partInventory->part->part_name); ?></td>
                    <td><?php echo e($partInventory->part->bin_location); ?></td>
                    <td><?php echo e($partInventory->site->site_name); ?></td>
                    <td><?php echo e($partInventory->min_stock); ?></td>
                    <td><?php echo e($partInventory->max_stock); ?></td>
                    <td><?php echo e($partInventory->stock_quantity); ?></td>
                    <td>
                        <button class="btn btn-danger btn-sm delete-part-btn" data-part-inventory-id="<?php echo e($partInventory->part_inventory_id); ?>">Delete</button>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="9">No parts found.</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <div class="mt-3">
            <div id="pagination-info" class="text-center mb-2 text-muted small">
                <!-- Page info will be displayed here -->
            </div>
            <div id="parts-pagination">
                <!-- Custom pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Kolom Kanan -->
    <div class="col max500 ml-2">
        <div class=" bgwhite shadow-kit rounded-lg">
            <div class="card-header">
                <h4 class="mb-0" id="formTitle">Kelompokkan Part kedalam Site</h4>
            </div>
            <div class="bgwhite rounded-lg p-4">
                <label for="partSearch" class="form-label">Cari Part:</label>
                <input type="text" id="partSearch" class="form-control" placeholder="Cari part...">
                <div id="suggestions" class="mt-2" style="display: none;"></div>

                <form id="addPartForm" class="mt-3">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="selectedPartName" class="form-label">Part yang Dipilih</label>
                        <input type="text" id="selectedPartName" class="form-control" readonly>
                        <input type="hidden" id="selectedPartCode">
                    </div>

                    <div id="siteCheckboxes">
                        <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="mb-2">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input site-checkbox" id="site_<?php echo e($site->site_id); ?>" value="<?php echo e($site->site_id); ?>">
                                <label class="form-check-label" for="site_<?php echo e($site->site_id); ?>"><?php echo e($site->site_name); ?></label>
                            </div>
                            <div id="siteInputs_<?php echo e($site->site_id); ?>" class="mt-1" style="display: none;">
                                <label for="sitePartName_<?php echo e($site->site_id); ?>" class="form-label">Site Part Name:</label>
                                <input type="text" id="sitePartName_<?php echo e($site->site_id); ?>" placeholder="Site-specific part name" class="form-control mb-1">
                                <label for="minStock_<?php echo e($site->site_id); ?>" class="form-label">Min Stock:</label>
                                <input type="number" id="minStock_<?php echo e($site->site_id); ?>" placeholder="Min Stock" class="form-control mb-1" value="0">
                                <label for="maxStock_<?php echo e($site->site_id); ?>" class="form-label">Max Stock:</label>
                                <input type="number" id="maxStock_<?php echo e($site->site_id); ?>" placeholder="Max Stock" class="form-control" value="0">
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <button type="submit" class="btn btn-primary mt-3">Simpan</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('resource'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/style.js', 'resources/js/part/Partgroup.js']); ?>
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
        max-width: 100%;
    }
    .pagination .page-item {
        margin: 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 10px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        min-width: 36px;
        text-align: center;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #parts-pagination {
        margin-top: 15px;
        width: 100%;
        overflow: hidden;
    }
</style>
<script>
    // Initial pagination data from server
    window.initialPaginationData = <?php echo json_encode($initialData ?? ['current_page' => 1, 'per_page' => 5, 'last_page' => 1, 'total' => 0]); ?>;
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/parts/pengelompokansitepart.blade.php ENDPATH**/ ?>