import Swal from 'sweetalert2';

$(document).ready(function() {
    // Load initial data
    loadJasaKaryawanData();

    // Handle filter button click
    $('#site-filter, #status-filter').change(function() {
        loadJasaKaryawanData();
    });

    // Handle date filter change
    $('#date-from, #date-to').change(function() {
        loadJasaKaryawanData();
    });

    // Handle search input
    $('#search-input').on('keyup', function(e) {
        if (e.key === 'Enter') {
            loadJasaKaryawanData();
        }
    });

    // Handle view detail button click (delegated event)
    $(document).on('click', '.btn-view-detail', function() {
        const id = $(this).data('id');
        viewJasaKaryawanDetail(id);
    });

    // No toggle needed as the form is always visible

    // Handle status update form submission
    $('#update-status-form').submit(function(e) {
        e.preventDefault();
        updateJasaKaryawanStatus();
    });

    // Handle pagination click (delegated event)
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const page = $(this).attr('href').split('page=')[1];
        loadJasaKaryawanData(page);
    });
});

// Function to loadMonthly Reportdata
function loadJasaKaryawanData(page = 1) {
    const siteId = $('#site-filter').val();
    const status = $('#status-filter').val();
    const dateFrom = $('#date-from').val();
    const dateTo = $('#date-to').val();
    const search = $('#search-input').val();

    $.ajax({
        url: '/sales/jasa-karyawan/data',
        type: 'GET',
        data: {
            page: page,
            site_id: siteId,
            status: status,
            date_from: dateFrom,
            date_to: dateTo,
            search: search,
            per_page: 10
        },
        beforeSend: function() {
            // Show loading indicator
            $('#jasa-karyawan-table-body').html('<tr><td colspan="8" class="text-center">Loading...</td></tr>');
        },
        success: function(response) {
            renderTable(response);
            renderPagination(response);
            updateEntriesInfo(response);
        },
        error: function(xhr) {
            console.error('Error loading data:', xhr);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Gagal memuat data. Silakan coba lagi.'
            });
        }
    });
}

// Function to render table data
function renderTable(response) {
    const data = response.data;
    let html = '';

    if (data.length === 0) {
        html = '<tr><td colspan="8" class="text-center">Tidak ada data</td></tr>';
    } else {
        data.forEach(function(item, index) {
            const date = new Date(item.date).toLocaleDateString('id-ID');
            const createdAt = new Date(item.created_at).toLocaleDateString('id-ID');

            let statusBadge = '';
            switch (item.status) {
                case 'submitted':
                    statusBadge = '<span class="status-badge status-submitted">Diajukan</span>';
                    break;
                case 'approved':
                    statusBadge = '<span class="status-badge status-approved">Disetujui</span>';
                    break;
                case 'rejected':
                    statusBadge = '<span class="status-badge status-rejected">Ditolak</span>';
                    break;
                case 'done':
                    statusBadge = '<span class="status-badge status-done">Selesai</span>';
                    break;
            }

            html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.site ? item.site.site_name : '-'}</td>
                    <td>${item.employee ? item.employee.username : '-'}</td>
                    <td>${date}</td>
                    <td>${item.formatted_amount}</td>
                    <td>${statusBadge}</td>
                    <td>${item.notes || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-info btn-view-detail" data-id="${item.id}">
                            <i class="mdi mdi-eye"></i> Detail
                        </button>
                    </td>
                </tr>
            `;
        });
    }

    $('#jasa-karyawan-table-body').html(html);
}

// Function to render pagination
function renderPagination(response) {
    let html = '';

    if (response.last_page > 1) {
        html = '<ul class="pagination pagination-sm">';

        // Previous page link
        if (response.current_page > 1) {
            html += `<li class="page-item"><a class="page-link" href="?page=${response.current_page - 1}">&laquo;</a></li>`;
        } else {
            html += '<li class="page-item disabled"><span class="page-link">&laquo;</span></li>';
        }

        // Page links
        for (let i = 1; i <= response.last_page; i++) {
            if (i === response.current_page) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                html += `<li class="page-item"><a class="page-link" href="?page=${i}">${i}</a></li>`;
            }
        }

        // Next page link
        if (response.current_page < response.last_page) {
            html += `<li class="page-item"><a class="page-link" href="?page=${response.current_page + 1}">&raquo;</a></li>`;
        } else {
            html += '<li class="page-item disabled"><span class="page-link">&raquo;</span></li>';
        }

        html += '</ul>';
    }

    $('#pagination-container').html(html);
}

// Function to update entries info
function updateEntriesInfo(response) {
    const start = (response.current_page - 1) * response.per_page + 1;
    const end = Math.min(start + response.per_page - 1, response.total);

    $('#entries-info').text(`Showing ${start} to ${end} of ${response.total} entries`);
}

// Function to viewMonthly Reportdetail
function viewJasaKaryawanDetail(id) {
    $.ajax({
        url: `/sales/jasa-karyawan/${id}`,
        type: 'GET',
        success: function(response) {
            // Set hidden ID field for status update
            $('#jasa-karyawan-id').val(response.id);

            // Populate detail fields
            $('#detail-id').text(response.id);
            $('#detail-site').text(response.site ? response.site.site_name : '-');
            $('#detail-employee').text(response.employee ? response.employee.username : '-');
            $('#detail-date').text(new Date(response.date).toLocaleDateString('id-ID'));
            $('#detail-amount').text(response.formatted_amount);

            // Set status with badge
            let statusBadge = '';
            switch (response.status) {
                case 'submitted':
                    statusBadge = '<span class="status-badge status-submitted">Diajukan</span>';
                    break;
                case 'approved':
                    statusBadge = '<span class="status-badge status-approved">Disetujui</span>';
                    break;
                case 'rejected':
                    statusBadge = '<span class="status-badge status-rejected">Ditolak</span>';
                    break;
                case 'done':
                    statusBadge = '<span class="status-badge status-done">Selesai</span>';
                    break;
            }
            $('#detail-status').html(statusBadge);

            $('#detail-notes').text(response.notes || '-');
            $('#detail-created-at').text(new Date(response.created_at).toLocaleString('id-ID'));

            // Set current status in the form
            $('#status').val(response.status);
            $('#notes').val(response.notes);

            // Handle file display
            if (response.file_path) {
                $('#no-file-message').addClass('d-none');
                $('#file-actions').removeClass('d-none');

                const fileUrl = `/assets/jasa_karyawan/${response.file_path}`;
                $('#download-file').attr('href', fileUrl);

                // Display file preview based on extension
                const fileExtension = response.file_path.split('.').pop().toLowerCase();

                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                    // Image file
                    $('#file-preview').html(`<img src="${fileUrl}" class="img-fluid" alt="Document Preview" style="max-width: 100%;">`);
                } else if (fileExtension === 'pdf') {
                    // PDF file
                    $('#file-preview').html(`<iframe src="${fileUrl}" width="100%" height="750px"></iframe>`);
                } else if (['xls', 'xlsx', 'doc', 'docx'].includes(fileExtension)) {
                    // Office documents
                    $('#file-preview').html(`
                        <div class="text-center p-4">
                            <i class="mdi ${fileExtension.includes('xls') ? 'mdi-file-excel' : 'mdi-file-word'}" style="font-size: 100px; color: ${fileExtension.includes('xls') ? '#1D6F42' : '#2B579A'};"></i>
                            <h4 class="mt-3">${response.file_path.split('_').slice(1).join('_')}</h4>
                            <p class="mb-4">File ${fileExtension.includes('xls') ? 'Excel' : 'Word'} tidak dapat ditampilkan secara langsung. Silakan download untuk melihat.</p>
                            <a href="${fileUrl}" class="btn btn-primary" target="_blank">
                                <i class="mdi mdi-download me-1"></i> Download File
                            </a>
                        </div>
                    `);
                } else {
                    // Other file types
                    $('#file-preview').html(`
                        <div class="text-center p-4">
                            <i class="mdi mdi-file-document-outline" style="font-size: 100px; color: #5f6368;"></i>
                            <h4 class="mt-3">${response.file_path.split('_').slice(1).join('_')}</h4>
                            <p class="mb-4">File tidak dapat ditampilkan. Silakan download untuk melihat.</p>
                            <a href="${fileUrl}" class="btn btn-primary" target="_blank">
                                <i class="mdi mdi-download me-1"></i> Download File
                            </a>
                        </div>
                    `);
                }
            } else {
                $('#no-file-message').removeClass('d-none');
                $('#file-actions').addClass('d-none');
                $('#file-preview').empty();
            }

            // Show modal
            $('#jasa-karyawan-detail-modal').modal('show');
        },
        error: function(xhr) {
            console.error('Error loading detail:', xhr);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Gagal memuat detail. Silakan coba lagi.'
            });
        }
    });
}

// Function to updateMonthly Reportstatus
function updateJasaKaryawanStatus() {
    const id = $('#jasa-karyawan-id').val();
    const status = $('#status').val();
    const notes = $('#notes').val();

    $.ajax({
        url: `/sales/jasa-karyawan/${id}/status`,
        type: 'PUT',
        data: {
            status: status,
            notes: notes,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        beforeSend: function() {
            // Disable submit button
            $('#update-status-form button[type="submit"]').prop('disabled', true).html('<i class="mdi mdi-spin mdi-loading"></i> Menyimpan...');
        },
        success: function(response) {
            // No need to hide the form as it's always visible

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Berhasil',
                text: response.message
            });

            // Reload data
            loadJasaKaryawanData();

            // Close modal
            $('#jasa-karyawan-detail-modal').modal('hide');
        },
        error: function(xhr) {
            console.error('Error updating status:', xhr);

            let errorMessage = 'Gagal memperbarui status. Silakan coba lagi.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: errorMessage
            });
        },
        complete: function() {
            // Re-enable submit button
            $('#update-status-form button[type="submit"]').prop('disabled', false).html('<i class="mdi mdi-content-save"></i> Simpan Perubahan');
        }
    });
}
