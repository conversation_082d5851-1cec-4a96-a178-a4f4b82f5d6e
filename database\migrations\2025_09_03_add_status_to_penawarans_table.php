<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penawarans', function (Blueprint $table) {
            // Add status column if it doesn't exist
            if (!Schema::hasColumn('penawarans', 'status')) {
                $table->enum('status', ['Draft', 'Dikirim ke customer', 'PO customer', 'Proses penyediaan', 'Selesai'])->default('Draft')->after('total');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penawarans', function (Blueprint $table) {
            if (Schema::hasColumn('penawarans', 'status')) {
                $table->dropColumn('status');
            }
        });
    }
};
