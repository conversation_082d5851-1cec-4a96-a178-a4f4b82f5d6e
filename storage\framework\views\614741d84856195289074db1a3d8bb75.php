<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title><?php echo $__env->yieldContent('title', 'DASHBOARD HO'); ?></title>
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <!-- Load library from CDN (browser-compatible UMD build) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/custombox@4.0.3/dist/custombox.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
    <!-- App css -->
    <?php echo app('Illuminate\Foundation\Vite')("resources/assets/css/bootstrap.min.css"); ?>
    <?php echo app('Illuminate\Foundation\Vite')("resources/assets/css/icons.min.css"); ?>
    <?php echo app('Illuminate\Foundation\Vite')("resources/assets/css/app.min.css"); ?>
    <?php echo app('Illuminate\Foundation\Vite')("resources/css/app.css"); ?>
</head>

<body>
    <div id="wrapper">
        <div class="left-side-menu" style="top: 0 !important;">
            <div class="slimscroll-menu">
                <div id="sidebar-menu">
                    <ul class="metismenu" id="side-menu">
                        <li class="menu-title">
                            Login sebagai <?php echo e(session('role')); ?>

                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'adminho.dashboard')); ?>">
                                <i class="mdi mdi-view-dashboard"></i>
                                <span> Dashboard </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'parts.index')); ?>">
                                <i class="mdi mdi-plus-box"></i>
                                <span> Buat Data Part</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'part-group.index')); ?>">
                                <i class="mdi mdi-map-marker"></i>
                                <span> Alokasi Part ke-Site </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('requisitionsho.index')); ?>">
                                <i class="mdi mdi-inbox-arrow-down"></i>
                                <span class="badge badge-success badge-pill float-right" id="jumlahpengajuan"></span>
                                <span> Pengajuan dari Site</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('equipment.index')); ?>">
                                <i class="mdi mdi-tools"></i>
                                <span> Perlengkapan </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'sites.index')); ?>">
                                <i class="mdi mdi-home-group"></i>
                                <span> Site dan Supplier </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'instock.index')); ?>">
                                <i class="mdi mdi-package-variant"></i>
                                <span> In Stock Part </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'out-parts.index')); ?>">
                                <i class="mdi mdi-package-down"></i>
                                <span> Our Stock Part </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'warehouse.transactions.index')); ?>">
                                <i class="mdi mdi-truck-delivery"></i>
                                <span> Pengiriman Ke Site </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'warehouse.transactions.history')); ?>">
                                <i class="mdi mdi-history"></i>
                                <span> Riwayat Pengiriman </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'warehouse.penawaran.index')); ?>">
                                <i class="mdi mdi-file-document-edit"></i>
                                <span> Penawaran Management </span>
                            </a>
                        </li>
                        <hr>
                        <li class="menu-title mt-2">Inventori</li>
                        <li style="z-index: 10;">
                            <a href="<?php echo e(route(name: 'inventori.card')); ?>">
                                <i class="mdi mdi-warehouse"></i>
                                <span> Inventory Card Site </span>
                                <span class="badge badge-danger align-right" id="jumalhsite"> </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'inventory_analysis.index')); ?>">
                                <i class="mdi mdi-chart-bar"></i>
                                <span> Best Inventory </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'returnpart')); ?>">
                                <i class="mdi mdi-history"></i>
                                <span> Return Part Dari Site</span>
                            </a>
                        </li>

                        <li>
                            <a href="<?php echo e(route(name: 'part-merge.index')); ?>">
                                <i class="mdi mdi-source-merge"></i>
                                <span> Merge Part</span>
                            </a>
                        </li>

                        <li>
                            <a href="<?php echo e(route(name: 'warehouse.jasa-karyawan.index')); ?>">
                                <i class="mdi mdi-account-hard-hat"></i>
                                <span> Monthly Report</span>
                            </a>
                        </li>

                        <hr>
                        <li class="menu-title mt-2">Log User</li>
                        <li>
                            <a href="<?php echo e(route(name: 'logaktivitas.index')); ?>">
                                <i class="mdi mdi-history"></i>
                                <span> History Log </span>
                            </a>
                        </li>
                        <?php if(Auth::user()->role == 'adminho' || Auth::user()->role == 'superadmin'): ?>
                        <li>
                            <a href="<?php echo e(route(name: 'password.token.generate')); ?>">
                                <i class="mdi mdi-key"></i>
                                <span> Generate Token </span>
                            </a>
                        </li>
                        <?php endif; ?>
                        <li>
                            <a href="<?php echo e(route(name: 'password.reset')); ?>">
                                <i class="mdi mdi-key-change"></i>
                                <span> Reset Password</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'logout')); ?>">
                                <i class="mdi mdi-logout"></i>
                                <span> Logout</span>
                            </a>
                        </li>
                    </ul>

                </div>
                <div class="clearfix"></div>
            </div>
        </div>

        <div class="content-page pt-0 mt-0 pr-0 mr-0">
            <div class="content">
                <div class="container-fluid p-0 mr-0">
                    <!-- ======================================================================================================================= -->
                    <!-- ======================================================================================================================= -->
                    <?php echo $__env->yieldContent('contentho'); ?>
                    <!-- ======================================================================================================================= -->
                    <!-- ======================================================================================================================= -->
                </div>
            </div>
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            2025 <a href="">PWB</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <?php echo app('Illuminate\Foundation\Vite')("resources/js/notifikasi.js"); ?>
    <?php echo app('Illuminate\Foundation\Vite')("resources/assets/js/app.min.js"); ?>
    <?php echo app('Illuminate\Foundation\Vite')("resources/js/adminho/public_adminho.js"); ?>
    <?php echo $__env->yieldContent('resource'); ?>

</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/content.blade.php ENDPATH**/ ?>