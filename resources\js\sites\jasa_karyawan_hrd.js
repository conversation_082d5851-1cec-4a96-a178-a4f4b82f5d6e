import Swal from 'sweetalert2';

$(document).ready(function() {
    // Initialize file input validation
    $('#file').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file size (max 10MB)
            const maxSize = 10 * 1024 * 1024; // 10MB in bytes
            if (file.size > maxSize) {
                Swal.fire({
                    icon: 'error',
                    title: 'File terlalu besar',
                    text: 'Ukuran file maksimal adalah 10MB'
                });
                this.value = '';
                return;
            }

            // Check file extension
            const allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx'];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!allowedExtensions.includes(fileExtension)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Format file tidak didukung',
                    text: 'Format file yang diizinkan: PDF, JPG, JPEG, PNG, DOC, DOCX, XLS, XLSX'
                });
                this.value = '';
                return;
            }

            // Show file name
            const fileName = file.name;
            $(this).next('.custom-file-label').html(fileName);
        }
    });

    // Load initial data
    loadJasaKaryawanData();

    // Add Monthly Report button click
    $('#btn-add-jasa-karyawan').click(function() {
        resetForm();
        $('#jasa-karyawan-modal-label').text('Tambah Monthly Report HRD');
        $('#jasa-karyawan-modal').modal('show');
    });

    // Save Monthly Report button click
    $('#btn-save-jasa-karyawan').click(function() {
        saveJasaKaryawan();
    });

    // Filter button click
    $('#btn-filter').click(function() {
        loadJasaKaryawanData();
    });

    // Search button click
    $('#btn-search').click(function() {
        loadJasaKaryawanData();
    });

    // Search on enter key
    $('#search-input').keypress(function(e) {
        if (e.which === 13) {
            loadJasaKaryawanData();
        }
    });

    // Handle edit button click (delegated event)
    $(document).on('click', '.btn-edit', function() {
        const id = $(this).data('id');
        editJasaKaryawan(id);
    });

    // Handle delete button click (delegated event)
    $(document).on('click', '.btn-delete', function() {
        const id = $(this).data('id');
        deleteJasaKaryawan(id);
    });

    // Handle view file button click (delegated event)
    $(document).on('click', '.btn-view-file', function() {
        const filePath = $(this).data('file');
        const fileName = $(this).data('filename');
        viewFile(filePath, fileName);
    });

    // Handle pagination click (delegated event)
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const page = $(this).attr('href').split('page=')[1];
        loadJasaKaryawanData(page);
    });
});

// Function to load Monthly Report data
function loadJasaKaryawanData(page = 1) {
    const search = $('#search-input').val();
    const status = $('#status-filter').val();
    const startDate = $('#start-date').val();
    const endDate = $('#end-date').val();

    $.ajax({
        url: '/jasa-karyawan/data',
        type: 'GET',
        data: {
            page: page,
            search: search,
            status: status,
            start_date: startDate,
            end_date: endDate,
            type: 'hrd',
            per_page: 5
        },
        beforeSend: function() {
            // Show loading indicator
            $('#jasa-karyawan-table tbody').html('<tr><td colspan="5" class="text-center">Loading...</td></tr>');
        },
        success: function(response) {
            renderTable(response);
            renderPagination(response);
        },
        error: function(xhr) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Gagal memuat data. Silakan coba lagi.'
            });
        }
    });
}

// Function to render table data
function renderTable(response) {
    const data = response.data;
    let html = '';

    if (data.length === 0) {
        html = '<tr><td colspan="5" class="text-center">Tidak ada data</td></tr>';
    } else {
        data.forEach(function(item) {
            const date = new Date(item.date).toLocaleDateString('id-ID');
            const amount = new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(item.amount);

            let statusBadge = '';
            switch (item.status) {
                case 'submitted':
                    statusBadge = '<span class="badge bg-warning">Submitted</span>';
                    break;
                case 'approved':
                    statusBadge = '<span class="badge bg-success">Approved</span>';
                    break;
                case 'rejected':
                    statusBadge = '<span class="badge bg-danger">Rejected</span>';
                    break;
                case 'done':
                    statusBadge = '<span class="badge bg-info">Done</span>';
                    break;
            }

            const fileButton = item.file_path
                ? `<button class="btn btn-sm btn-info btn-view-file" data-file="${item.file_path}" data-filename="${item.file_path.split('_').slice(1).join('_')}">
                     <i class="mdi mdi-file-document-outline"></i>
                   </button>`
                : '<span class="text-muted">Tidak ada file</span>';

            const actions = item.status === 'done'
                ? '<span class="text-muted">Tidak ada aksi</span>'
                : `<div class="btn-group">
                     <button class="btn btn-sm btn-primary btn-edit" data-id="${item.id}">
                       <i class="mdi mdi-pencil"></i>
                     </button>
                     <button class="btn btn-sm btn-danger btn-delete" data-id="${item.id}">
                       <i class="mdi mdi-delete"></i>
                     </button>
                   </div>`;

            html += `
                <tr>
                    <td>${item.id}</td>
                    <td>${date}</td>
                    <td>${statusBadge}</td>
                    <td>${fileButton}</td>
                    <td>${actions}</td>
                </tr>
            `;
        });
    }

    $('#jasa-karyawan-table tbody').html(html);
}

// Function to render pagination
function renderPagination(response) {
    let html = '';

    if (response.last_page > 1) {
        html = '<ul class="pagination">';

        // Previous page link
        if (response.current_page > 1) {
            html += `<li class="page-item"><a class="page-link" href="?page=${response.current_page - 1}">&laquo;</a></li>`;
        } else {
            html += '<li class="page-item disabled"><span class="page-link">&laquo;</span></li>';
        }

        // Page links
        for (let i = 1; i <= response.last_page; i++) {
            if (i === response.current_page) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                html += `<li class="page-item"><a class="page-link" href="?page=${i}">${i}</a></li>`;
            }
        }

        // Next page link
        if (response.current_page < response.last_page) {
            html += `<li class="page-item"><a class="page-link" href="?page=${response.current_page + 1}">&raquo;</a></li>`;
        } else {
            html += '<li class="page-item disabled"><span class="page-link">&raquo;</span></li>';
        }

        html += '</ul>';
    }

    $('.pagination-container').html(html);
}

// Function to save Monthly Report
function saveJasaKaryawan() {
    const form = $('#jasa-karyawan-form')[0];
    const formData = new FormData(form);
    const id = $('#jasa-karyawan-id').val();

    // Determine if this is an update or create operation
    const url = id ? `/jasa-karyawan/${id}` : '/jasa-karyawan';
    const method = id ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        type: method,
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        beforeSend: function() {
            $('#btn-save-jasa-karyawan').prop('disabled', true).html('<i class="mdi mdi-spin mdi-loading"></i> Menyimpan...');
        },
        success: function(response) {
            $('#jasa-karyawan-modal').modal('hide');
            Swal.fire({
                icon: 'success',
                title: 'Berhasil',
                text: response.message
            });
            loadJasaKaryawanData();
        },
        error: function(xhr) {
            let errorMessage = 'Terjadi kesalahan. Silakan coba lagi.';

            if (xhr.responseJSON && xhr.responseJSON.errors) {
                const errors = xhr.responseJSON.errors;
                errorMessage = Object.values(errors).flat().join('<br>');
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                icon: 'error',
                title: 'Error',
                html: errorMessage
            });
        },
        complete: function() {
            $('#btn-save-jasa-karyawan').prop('disabled', false).text('Simpan');
        }
    });
}

// Function to edit Monthly Report
function editJasaKaryawan(id) {
    $.ajax({
        url: `/jasa-karyawan/data?id=${id}&type=hrd`,
        type: 'GET',
        success: function(response) {
            const item = response.data.find(item => item.id == id);

            if (item) {
                resetForm();

                $('#jasa-karyawan-id').val(item.id);
                $('#date').val(item.date);
                $('#amount').val(0);
                $('#notes').val('');
                $('#type').val('hrd');

                // Reset file input
                $('#file').val('');
                $('#file').next('.custom-file-label').html('');

                $('#jasa-karyawan-modal-label').text('Edit Monthly Report HRD');
                $('#jasa-karyawan-modal').modal('show');
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Data tidak ditemukan'
                });
            }
        },
        error: function(xhr) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Gagal memuat data. Silakan coba lagi.'
            });
        }
    });
}

// Function to delete Monthly Report
function deleteJasaKaryawan(id) {
    Swal.fire({
        title: 'Konfirmasi',
        text: 'Apakah Anda yakin ingin menghapus data ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/jasa-karyawan/${id}`,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: response.message
                    });
                    loadJasaKaryawanData();
                },
                error: function(xhr) {
                    let errorMessage = 'Terjadi kesalahan. Silakan coba lagi.';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errorMessage
                    });
                }
            });
        }
    });
}

// Function to view file
function viewFile(filePath, fileName) {
    const fileUrl = `/assets/jasa_karyawan/${filePath}`;
    const fileExtension = fileName.split('.').pop().toLowerCase();

    // Clear previous content
    $('#file-container').empty();

    // Set download link
    $('#download-file').attr('href', fileUrl);

    // Display file based on extension
    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
        // Image file
        $('#file-container').html(`<img src="${fileUrl}" class="img-fluid" alt="${fileName}">`);
    } else if (fileExtension === 'pdf') {
        // PDF file
        $('#file-container').html(`<iframe src="${fileUrl}" width="100%" height="500px"></iframe>`);
    } else {
        // Other file types
        $('#file-container').html(`
            <div class="text-center">
                <i class="mdi mdi-file-document-outline" style="font-size: 72px;"></i>
                <p>File tidak dapat ditampilkan. Silakan download untuk melihat.</p>
                <p>Nama file: ${fileName}</p>
            </div>
        `);
    }

    // Show modal
    $('#view-file-modal').modal('show');
}

// Function to reset form
function resetForm() {
    $('#jasa-karyawan-form')[0].reset();
    $('#jasa-karyawan-id').val('');
    $('#type').val('hrd');

    // Reset file input
    $('#file').val('');
    $('#file').next('.custom-file-label').html('');
}
