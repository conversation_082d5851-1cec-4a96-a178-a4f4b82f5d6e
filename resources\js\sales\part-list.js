// Part List Management
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = 1;
    let currentSearch = '';
    let partTypes = [];

    // Initialize
    init();

    function init() {
        loadPartTypes();
        loadPartsData();
        setupEventListeners();
        setupCurrencyFormatting();
    }

    function setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearch = this.value;
                    currentPage = 1;
                    loadPartsData();
                }, 500);
            });
        }

        // Add new part button
        const addNewPartBtn = document.getElementById('add-new-part-btn');
        if (addNewPartBtn) {
            addNewPartBtn.addEventListener('click', function() {
                openAddPartModal();
            });
        }

        // Save part button
        const savePartBtn = document.getElementById('save-part-btn');
        if (savePartBtn) {
            savePartBtn.addEventListener('click', function() {
                saveNewPart();
            });
        }

        // Update part button
        const updatePartBtn = document.getElementById('update-part-btn');
        if (updatePartBtn) {
            updatePartBtn.addEventListener('click', function() {
                updatePart();
            });
        }

        // Modal close buttons
        document.querySelectorAll('.close-modal-btn, [data-bs-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', function() {
                closeModals();
            });
        });
    }

    function loadPartTypes() {
        fetch('/sales/part-list/part-types')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    partTypes = data.data;
                }
            })
            .catch(error => {
                console.error('Error loading part types:', error);
            });
    }

    function loadPartsData() {
        showLoading(true);

        const params = new URLSearchParams({
            page: currentPage,
            search: currentSearch
        });

        fetch(`/sales/part-list/data?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderPartsTable(data.data);
                    renderPagination(data.pagination);
                    updateShowingText(data.pagination);
                } else {
                    showError('Gagal memuat data part');
                }
            })
            .catch(error => {
                console.error('Error loading parts:', error);
                showError('Terjadi kesalahan saat memuat data');
            })
            .finally(() => {
                showLoading(false);
            });
    }

    function renderPartsTable(parts) {
        const tbody = document.getElementById('parts-table-body');
        if (!tbody) return;

        if (parts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="mdi mdi-package-variant-closed"></i><br>
                        Tidak ada data part ditemukan
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = parts.map(part => `
            <tr data-part-code="${part.part_code}">
                <td>${part.part_code}</td>
                <td>${part.part_name || ''}</td>
                <td>${part.part_type || ''}</td>
                <td class="currency">${formatCurrency(part.purchase_price)}</td>
                <td class="currency">${formatCurrency(part.price)}</td>
                <td class="text-center">${part.eum || ''}</td>
                <td class="action-buttons">
                    <button class="btn-edit" onclick="editPart('${part.part_code}')">
                        <i class="mdi mdi-pencil"></i> Edit
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Currency formatting function
    function formatCurrency(value) {
        if (!value || value === 0) return '-';
        return 'Rp ' + parseFloat(value).toLocaleString('id-ID');
    }

    // Currency input formatting
    function setupCurrencyFormatting() {
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('currency-input')) {
                formatCurrencyInput(e.target);
            }
        });
    }

    function formatCurrencyInput(input) {
        let value = input.value.replace(/[^\d]/g, '');
        if (value) {
            input.value = 'Rp ' + parseInt(value).toLocaleString('id-ID');
        }
    }

    function parseCurrencyValue(value) {
        if (!value) return null;
        return parseFloat(value.replace(/[^\d]/g, '')) || null;
    }

    function renderPagination(pagination) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        let paginationHTML = '';

        // Previous button
        if (pagination.current_page > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${pagination.current_page - 1})">
                        <i class="mdi mdi-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }

        // Next button
        if (pagination.current_page < pagination.last_page) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${pagination.current_page + 1})">
                        <i class="mdi mdi-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    function updateShowingText(pagination) {
        const showingText = document.getElementById('showing-text');
        if (showingText) {
            showingText.textContent = `Menampilkan ${pagination.from || 0} - ${pagination.to || 0} dari ${pagination.total} part`;
        }
    }

    // Global functions
    window.changePage = function(page) {
        currentPage = page;
        loadPartsData();
    };

    // Modal functions
    function openAddPartModal() {
        // Reset form
        document.getElementById('add-part-form').reset();
        document.getElementById('add_eum').value = 'EA';

        // Show modal
        const modal = document.getElementById('add-part-modal');
        modal.classList.add('show');
        modal.style.display = 'block';
        document.body.classList.add('modal-open');

        // Add backdrop
        if (!document.querySelector('.modal-backdrop')) {
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
    }

    window.editPart = function(partCode) {
        // Find part data
        const row = document.querySelector(`tr[data-part-code="${partCode}"]`);
        if (!row) return;

        // Get current data from the table
        const cells = row.querySelectorAll('td');
        const partData = {
            part_code: partCode,
            part_name: cells[1].textContent,
            part_type: cells[2].textContent,
            price: parseCurrencyValue(cells[3].textContent),
            purchase_price: parseCurrencyValue(cells[4].textContent),
            eum: cells[5].textContent
        };

        // Populate edit form
        document.getElementById('edit_part_code_hidden').value = partData.part_code;
        document.getElementById('edit_part_code').value = partData.part_code;
        document.getElementById('edit_part_name').value = partData.part_name;
        document.getElementById('edit_part_type').value = partData.part_type;
        document.getElementById('edit_price').value = partData.price ? 'Rp ' + partData.price.toLocaleString('id-ID') : '';
        document.getElementById('edit_purchase_price').value = partData.purchase_price ? 'Rp ' + partData.purchase_price.toLocaleString('id-ID') : '';
        document.getElementById('edit_eum').value = partData.eum;

        // Show modal
        const modal = document.getElementById('edit-part-modal');
        modal.classList.add('show');
        modal.style.display = 'block';
        document.body.classList.add('modal-open');

        // Add backdrop
        if (!document.querySelector('.modal-backdrop')) {
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
    };

    function saveNewPart() {
        const form = document.getElementById('add-part-form');
        const formData = new FormData(form);

        // Convert currency values
        const price = parseCurrencyValue(formData.get('price'));
        const purchasePrice = parseCurrencyValue(formData.get('purchase_price'));

        const data = {
            part_code: formData.get('part_code'),
            part_name: formData.get('part_name'),
            part_type: formData.get('part_type'),
            price: price,
            purchase_price: purchasePrice,
            eum: formData.get('eum') || 'EA'
        };

        showLoading(true);

        fetch('/sales/part-list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                closeModals();
                loadPartsData(); // Reload to show new data
            } else {
                if (data.errors) {
                    const errorMessages = Object.values(data.errors).flat().join('<br>');
                    showError(errorMessages);
                } else {
                    showError(data.message || 'Gagal menambahkan part');
                }
            }
        })
        .catch(error => {
            console.error('Error saving part:', error);
            showError('Terjadi kesalahan saat menambahkan part');
        })
        .finally(() => {
            showLoading(false);
        });
    }

    function updatePart() {
        const form = document.getElementById('edit-part-form');
        const formData = new FormData(form);
        const partCode = formData.get('part_code');

        // Convert currency values
        const price = parseCurrencyValue(formData.get('price'));
        const purchasePrice = parseCurrencyValue(formData.get('purchase_price'));

        const data = {
            part_name: formData.get('part_name'),
            part_type: formData.get('part_type'),
            price: price,
            purchase_price: purchasePrice,
            eum: formData.get('eum')
        };

        showLoading(true);

        fetch(`/sales/part-list/${partCode}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                closeModals();
                loadPartsData(); // Reload to show updated data
            } else {
                if (data.errors) {
                    const errorMessages = Object.values(data.errors).flat().join('<br>');
                    showError(errorMessages);
                } else {
                    showError(data.message || 'Gagal memperbarui part');
                }
            }
        })
        .catch(error => {
            console.error('Error updating part:', error);
            showError('Terjadi kesalahan saat memperbarui part');
        })
        .finally(() => {
            showLoading(false);
        });
    }

    function closeModals() {
        // Close all modals
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
            modal.style.display = 'none';
        });

        document.body.classList.remove('modal-open');

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }

    function showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.toggle('d-none', !show);
        }
    }

    function showSuccess(message) {
        // Using SweetAlert if available, otherwise alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        } else {
            alert(message);
        }
    }

    function showError(message) {
        // Using SweetAlert if available, otherwise alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                html: message,
                confirmButtonText: 'OK'
            });
        } else {
            alert(message);
        }
    }
});
