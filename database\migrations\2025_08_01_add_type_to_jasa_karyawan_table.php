<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jasa_karyawan', function (Blueprint $table) {
            $table->enum('type', ['hrd', 'sales'])->default('hrd')->after('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jasa_karyawan', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
