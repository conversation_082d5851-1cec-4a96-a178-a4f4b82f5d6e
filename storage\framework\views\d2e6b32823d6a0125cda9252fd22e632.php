
<?php $__env->startSection('contentsite'); ?>
<?php $__env->startSection('title', 'Dashboard Site'); ?>
<!-- mulai content disin -->
<div class="row bgwhite page-title-box shadow-kit mb-1 d-flex align-items-center">
    <div class="col" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
    </div>

    <div class="col">
        <form method="GET" action="<?php echo e(route('sites.dashboard')); ?>" class="p-2">
            <div class="row align-items-center">
                <div class="col-auto">
                    <div class="d-flex align-items-center gap-3">
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input custom-radio" name="group_by" id="day" value="day" <?php echo e($groupBy === 'day' ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="day">Day</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input custom-radio" name="group_by" id="week" value="week" <?php echo e($groupBy === 'week' ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="week">Week</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input custom-radio" name="group_by" id="month" value="month" <?php echo e($groupBy === 'month' ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="month">Month</label>
                        </div>
                    </div>
                </div>
                <div class="col-auto">
                    <input type="date" class="form-control" name="start_date" value="<?php echo e($startDate); ?>">
                </div>
                <div class="col-auto">
                    <input type="date" class="form-control" name="end_date" value="<?php echo e($endDate); ?>">
                </div>
            </div>
        </form>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row pt-1 pl-2">
    <div class="col p-0">
        <div class="shadow-kit bgchart">
            <h5 class="pl-4 pt-3 mt-0 text-uppercase">In & Out Stock Chart</h5>
            <div style="min-height: 400px;" class="chart-container">
                <canvas id="inOutStockChart"></canvas>
            </div>
        </div>
        <?php $__currentLoopData = $inventoryData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(count($data['not_ready_parts']) > 0 || count($data['medium_parts']) > 0): ?>
        <div class="pl-2 pr-4 shadow-kit bgwhite p-4 pb-0 mt-2">
            <h5 class="text-uppercase text-bold">Status Part di <?php echo e($data['site_name']); ?></h5>
            <hr>
            <?php if(count($data['not_ready_parts']) > 0): ?>
            <div class="table-responsive">
                <h6>Part Not Ready</h6>
                <table class="table table-bordered w-100" id="not-ready-table-<?php echo e($loop->index); ?>">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">Nama Part</th>
                            <th class="p-2">Min</th>
                            <th class="p-2">Max</th>
                            <th class="p-2">Stock Tersisa</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $data['not_ready_parts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $part): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="<?php echo e($part['status'] == 'danger' ? 'table-danger' : ''); ?>">
                            <td><?php echo e($part['part_name']); ?></td>
                            <td><?php echo e($part['min_stock']); ?></td>
                            <td><?php echo e($part['max_stock']); ?></td>
                            <td><?php echo e($part['stock_quantity']); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
                <div id="not-ready-pagination-<?php echo e($loop->index); ?>" class="pagination-container mt-3">
                    <!-- Pagination will be rendered here by JavaScript -->
                </div>
            </div>
            <?php endif; ?>

            <?php if(count($data['medium_parts']) > 0): ?>
            <div class="table-responsive">
                <h6>Part Hampir Habis (Stock mendekati Minimum)</h6>
                <table class="table table-bordered w-100" id="medium-table-<?php echo e($loop->index); ?>">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">Nama Part</th>
                            <th class="p-2">Min</th>
                            <th class="p-2">Max</th>
                            <th class="p-2">Stock Tersisa</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $data['medium_parts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $part): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="<?php echo e($part['status'] == 'warning' ? 'table-warning' : 'table-warning'); ?>">
                            <td><?php echo e($part['part_name']); ?></td>
                            <td><?php echo e($part['min_stock']); ?></td>
                            <td><?php echo e($part['max_stock']); ?></td>
                            <td><?php echo e($part['stock_quantity']); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
                <div id="medium-pagination-<?php echo e($loop->index); ?>" class="pagination-container mt-3">
                    <!-- Pagination will be rendered here by JavaScript -->
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <div class="col pr-2 pl-2 ml-2 mr-2">
        <div class="row">
            <div class="col">
                <div class="">
                    <div class="m-0">
                        <div class="p-2 align-items-center shadow-kit bgwhite mb-1">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calculator fa-3x text-danger mr-3"></i>
                                    <div>
                                        <h5 class="card-title font-weight-bold">Total Pendapatan</h5>
                                        <h3 class="h2 card-text font-weight-bold mb-0" id="totalUnitTransactions"><?php echo e($formattedTotalUnitTransactions); ?></h3>
                                        <small class="text-muted">Berdasarkan Tanggal : <?php echo e(\Carbon\Carbon::parse($startDate)->format('d-m-Y')); ?> s/d <?php echo e(\Carbon\Carbon::parse($endDate)->format('d-m-Y')); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body bgwhite shadow-kit">
                        <div class="pb-0 d-flex align-items-center">
                            <i class="fas fa-arrow-alt-circle-down fa-3x text-success mr-3"></i> <!-- Ikon In-Stock -->
                            <div>
                                <h5 class="card-title font-weight-bold m-0">Total In-Stocks</h5>
                                <p class="card-text display-4 font-weight-bold mb-0" id="totalInStock"><?php echo e($totalInStock); ?></p>
                            </div>
                        </div>
                    </div>
                    <hr class="m-1 p-0">
                    <div class="card-body bgwhite shadow-kit">
                        <div class="pt-0 d-flex align-items-center">
                            <i class="fas fa-arrow-alt-circle-up fa-3x text-secondary mr-3"></i> <!-- Ikon Out-Stock -->
                            <div>
                                <h5 class="card-title font-weight-bold m-0">Total Out-Stocks</h5>
                                <!-- Add ID to this element -->
                                <p class="card-text display-4 font-weight-bold mb-0" id="totalOutStock"><?php echo e($totalOutStock); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="bgwhite shadow-kit">
                    <h5 class="pt-3 pl-4 mt-0 text-uppercase font-bold"> Status Stock</h5>
                    <?php if(isset($inventoryData)): ?>
                    <?php $__currentLoopData = $inventoryData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-0">
                        <div class="card-header"><?php echo e($data['site_name']); ?> - Inventory Status</div>
                        <div class="">
                            <canvas id="pieChart<?php echo e(Str::slug($data['site_name'])); ?>" width="300" height="350"></canvas>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                    <p>No inventory data available.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col p-0">
                <div class="shadow-kit bgwhite p-4 mt-2">
                    <h3>Your Lats Activities</h3>
                    <hr class="mt-2">
                    <ul id="activityList">
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="exportModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="font-bold text-uppercase" id="title">Filter Laporan</h5>
                <span class="close" aria-label="Close">&times;</span>
            </div>
            <form id="exportForm" method="GET">
                <input type="hidden" name="report_type" id="reportType">
                <div class="modal-body">
                    <div class="form-grid">
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="start_date">Tanggal Mulai</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="end_date">Tanggal Selesai</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>
                        </div>
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="sort_by">Urutkan Berdasarkan</label>
                                <select class="custom-select" name="sort_by">
                                    <option value="date">Tanggal</option>
                                    <option value="part_name">Nama Part</option>
                                </select>
                            </div>
                            <div class="form-group flex-grow-1 hidden">
                                <label for="site_id">Site</label>
                                <select class="custom-select" name="site_id">
                                    <option value="<?php echo e(session('site_id')); ?>" selected>Semua Site</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-secondary" id="closeModal">Tutup</button> -->
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportPdf">
                        <i class="mdi mdi-file-pdf-box mr-1"></i> Export PDF
                    </button>
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportExcel">
                        <i class="mdi mdi-file-excel mr-1"></i> Export Excel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/site/Chartsite.js','resources/js/style.js','resources/js/piechart.js']); ?>
<!-- Pagination Styling -->
<style>
    .pagination-container {
        display: flex;
        justify-content: center;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .pagination li {
        margin: 0 2px;
    }

    .pagination li a {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
        display: block;
    }

    .pagination li.active a {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination li a:hover {
        background-color: #f8f9fa;
    }

    .pagination li.disabled a {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #ddd;
    }
</style>

<?php echo app('Illuminate\Foundation\Vite')('resources/js/site/Dashboard.js'); ?>
<?php echo app('Illuminate\Foundation\Vite')('resources/js/laporan.js'); ?>
<script>
    window.chartData = <?php echo json_encode($chartData, 15, 512) ?>;
    window.groupBy = <?php echo json_encode($groupBy, 15, 512) ?>;
    window.inventoryData = <?php echo json_encode($inventoryData, 15, 512) ?>;

    // Pagination functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Function to create pagination for a table
        function setupPagination(tableId, paginationId, itemsPerPage = 5) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');
            const totalPages = Math.ceil(rows.length / itemsPerPage);

            let currentPage = 1;

            // Function to show the appropriate rows for the current page
            function showPage(page) {
                const start = (page - 1) * itemsPerPage;
                const end = start + itemsPerPage;

                // Hide all rows
                rows.forEach((row, index) => {
                    row.style.display = (index >= start && index < end) ? '' : 'none';
                });

                // Update pagination UI
                updatePagination();
            }

            // Function to create pagination controls
            function updatePagination() {
                const paginationContainer = document.getElementById(paginationId);
                if (!paginationContainer) return;

                paginationContainer.innerHTML = '';

                if (totalPages <= 1) return; // Don't show pagination if only one page

                const ul = document.createElement('ul');
                ul.className = 'pagination pagination-rounded ';

                // Previous button
                const prevLi = document.createElement('li');
                prevLi.className = currentPage === 1 ? 'disabled' : '';
                const prevA = document.createElement('a');
                prevA.href = '#';
                prevA.textContent = '«';
                prevA.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage > 1) {
                        currentPage--;
                        showPage(currentPage);
                    }
                });
                prevLi.appendChild(prevA);
                ul.appendChild(prevLi);

                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    const li = document.createElement('li');
                    li.className = i === currentPage ? 'active' : '';
                    const a = document.createElement('a');
                    a.href = '#';
                    a.textContent = i;
                    a.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        showPage(currentPage);
                    });
                    li.appendChild(a);
                    ul.appendChild(li);
                }

                // Next button
                const nextLi = document.createElement('li');
                nextLi.className = currentPage === totalPages ? 'disabled' : '';
                const nextA = document.createElement('a');
                nextA.href = '#';
                nextA.textContent = '»';
                nextA.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage < totalPages) {
                        currentPage++;
                        showPage(currentPage);
                    }
                });
                nextLi.appendChild(nextA);
                ul.appendChild(nextLi);

                paginationContainer.appendChild(ul);
            }

            // Initialize pagination
            showPage(currentPage);
        }

        // Setup pagination for all tables
        const inventoryData = window.inventoryData || [];
        inventoryData.forEach((data, index) => {
            if (data.not_ready_parts && data.not_ready_parts.length > 0) {
                setupPagination(`not-ready-table-${index}`, `not-ready-pagination-${index}`, 5);
            }
            if (data.medium_parts && data.medium_parts.length > 0) {
                setupPagination(`medium-table-${index}`, `medium-pagination-${index}`, 5);
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sites/dashboard.blade.php ENDPATH**/ ?>