<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JasaKaryawan extends Model
{
    use HasFactory;

    protected $table = 'jasa_karyawan';
    protected $fillable = [
        'site_id',
        'employee_id',
        'file_path',
        'date',
        'amount',
        'status',
        'notes',
        'type',
    ];

    protected $casts = [
        'date' => 'date',
        'amount' => 'decimal:2',
    ];

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }

    public function employee()
    {
        return $this->belongsTo(User::class, 'employee_id', 'employee_id');
    }
}
