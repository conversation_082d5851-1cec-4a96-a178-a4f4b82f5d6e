@extends('warehouse.content')
@section('contentho')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('adminho.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Monthly Report</li>
                    </ol>
                </div>
                <h4 class="page-title">Monthly Report</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h4 class="header-title">Daftar Monthly Report</h4>
                            <p class="text-muted">
                                Kelola dataMonthly Reportwarehouse
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <button type="button" class="btn btn-primary" id="add-jasa-karyawan-btn" onclick="openAddModal()">
                                <i class="mdi mdi-plus-circle me-1"></i> tambah Monthly Report
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3 mb-2">
                            <label for="search-input" class="form-label">Pencarian</label>
                            <input type="text" class="form-control" id="search-input" placeholder="Cari...">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="status-filter" class="form-label">Status</label>
                            <select class="form-select" id="status-filter">
                                <option value="">Semua Status</option>
                                <option value="submitted">Diajukan</option>
                                <option value="approved">Disetujui</option>
                                <option value="rejected">Ditolak</option>
                                <option value="done">Selesai</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="start-date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start-date">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="end-date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end-date">
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-12 text-end">
                            <button type="button" class="btn btn-sm btn-info" id="filter-btn">
                                <i class="mdi mdi-filter me-1"></i> Filter
                            </button>
                            <button type="button" class="btn btn-sm btn-secondary" id="reset-filter-btn">
                                <i class="mdi mdi-refresh me-1"></i> Reset
                            </button>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-centered table-striped dt-responsive nowrap w-100" id="jasa-karyawan-table">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Jumlah</th>
                                    <th>Status</th>
                                    <th>Catatan</th>
                                    <th>File</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="pagination-container d-flex justify-content-end">
                                <!-- Pagination will be loaded here via AJAX -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/EditMonthly ReportModal -->
<div class="modal fade" id="jasa-karyawan-modal" tabindex="-1" aria-labelledby="jasa-karyawan-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jasa-karyawan-modal-label">Tambah Monthly Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="jasa-karyawan-form" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" id="jasa-karyawan-id" name="id">

                    <div class="mb-3">
                        <label for="date" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="date" name="date" required>
                    </div>

                    <div class="mb-3">
                        <label for="amount" class="form-label">Jumlah (Rp)</label>
                        <input type="text" class="form-control" id="amount" name="amount" required>
                        <input type="hidden" id="amount_raw" name="amount_raw">
                        <script>
                            document.getElementById('amount').addEventListener('input', function(e) {
                                // Remove non-numeric characters
                                let value = this.value.replace(/\D/g, '');
                                
                                // Store raw value in hidden input
                                document.getElementById('amount_raw').value = value;
                                
                                // Format the number
                                if (value) {
                                    value = parseInt(value);
                                    this.value = new Intl.NumberFormat('id-ID').format(value);
                                } else {
                                    this.value = '';
                                }
                            });

                            // Add event listener for modal hidden event
                            document.getElementById('jasa-karyawan-modal').addEventListener('hidden.bs.modal', function () {
                                // Remove modal backdrop
                                const backdrop = document.querySelector('.modal-backdrop');
                                if (backdrop) {
                                    backdrop.remove();
                                }
                                // Reset body classes
                                document.body.classList.remove('modal-open');
                                document.body.style.overflow = '';
                                document.body.style.paddingRight = '';
                            });
                        </script>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Catatan</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="file" class="form-label">File (PDF, JPG, JPEG, PNG, DOC, DOCX, XLS, XLSX, max 10MB)</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx">
                        <div id="file-preview" class="mt-2 d-none">
                            <p>File saat ini: <span id="current-file-name"></span></p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="save-jasa-karyawan-btn">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- View File Modal -->
<div class="modal fade" id="view-file-modal" tabindex="-1" aria-labelledby="view-file-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="view-file-modal-label">Lihat File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Detail Monthly Report</h5>
                                <div class="mb-2">
                                    <strong>Tanggal:</strong> <span id="detail-date"></span>
                                </div>
                                <div class="mb-2">
                                    <strong>Jumlah:</strong> <span id="detail-amount"></span>
                                </div>
                                <div class="mb-2">
                                    <strong>Status:</strong> <span id="detail-status"></span>
                                </div>
                                <div class="mb-2">
                                    <strong>Catatan:</strong> <span id="detail-notes"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div id="file-container" class="text-center">
                            <!-- File will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="#" class="btn btn-primary" id="download-file-btn" target="_blank">Download File</a>
            </div>
        </div>
    </div>
</div>

@endsection

@section('resource')
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // Function to open the add modal directly from HTML
    function openAddModal() {
        console.log('openAddModal called');
        // Reset the form
        document.getElementById('jasa-karyawan-form').reset();
        document.getElementById('jasa-karyawan-id').value = '';
        document.getElementById('file-preview').classList.add('d-none');

        // Set the modal title
        document.getElementById('jasa-karyawan-modal-label').textContent = 'Tambah Monthly Report';

        // Show the modal using Bootstrap 5
        try {
            var modal = new bootstrap.Modal(document.getElementById('jasa-karyawan-modal'));
            modal.show();
        } catch (e) {
            console.error('Error showing modal:', e);
            alert('Terjadi kesalahan saat membuka modal. Silakan coba lagi.');
        }
    }
</script>
@vite(['resources/js/warehouse/jasa_karyawan.js'])
@endsection


