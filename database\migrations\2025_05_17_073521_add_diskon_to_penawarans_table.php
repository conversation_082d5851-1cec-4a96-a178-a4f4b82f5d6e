<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penawarans', function (Blueprint $table) {
            // Add diskon column if it doesn't exist
            if (!Schema::hasColumn('penawarans', 'diskon')) {
                $table->decimal('diskon', 5, 2)->default(0)->after('notes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penawarans', function (Blueprint $table) {
            if (Schema::hasColumn('penawarans', 'diskon')) {
                $table->dropColumn('diskon');
            }
        });
    }
};
