<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add direct_subtotal column if it doesn't exist
            if (!Schema::hasColumn('invoices', 'direct_subtotal')) {
                $table->decimal('direct_subtotal', 15, 2)->nullable()->after('penawaran_id');
            }
            
            // Add site_id column if it doesn't exist
            if (!Schema::hasColumn('invoices', 'site_id')) {
                $table->foreignId('site_id')->nullable()->after('penawaran_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            if (Schema::hasColumn('invoices', 'direct_subtotal')) {
                $table->dropColumn('direct_subtotal');
            }
            
            if (Schema::hasColumn('invoices', 'site_id')) {
                $table->dropColumn('site_id');
            }
        });
    }
};
