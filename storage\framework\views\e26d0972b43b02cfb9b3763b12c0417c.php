
<?php $__env->startSection('resource'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/style.js','resources/js/pengajuan/Confirmationho.js']); ?>
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }

    #requisitions-pagination {
        margin-top: 15px;
    }
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('contentho'); ?>
<?php $__env->startSection('title', 'Konfirmasi Pengajuan'); ?>
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0"><?php echo e(session('name')); ?></p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="m-2">
    <div class="row shadow-kit bgwhite rounded-lg mb-2">
        <div class="shadow-kit col-md-12" id="detailSection" style="display:none;">
            <div class=" p-4">
                <p class="h4 font-bold">LIST PART YANG DIAJUKAN</p>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Nama Part</th>
                            <th>Catatan site</th>
                            <th>Jumlah Diajuan Site</th>
                            <th>Jumlah Diterima Site</th>
                            <th>Dikirim Ke Site</th>
                            <th>Proses Pengiriman</th>
                            <th>Status</th>
                            <th>Berikan keterangan <span class="text-green-400">*Opsinal</span></th>
                            <th>Lampiran <span class="text-green-400">*Opsional</span></th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="detailList">
                    </tbody>
                </table>
                <div class="float-right mb-4">
                    <button id="approveAll" class="btn btn-sm btn-success">Setujui Semua</button>
                    <button id="btntutupapproveAll" class="btn btn-sm btn-secondary">Tutup</button>
                </div>
            </div>
        </div>
        <div class="col p-4">
            <h4 class="font-bold h4">PENGAJUAN PART DARI SITE</h4>

            <!-- Date Filter Section -->
            <div class="row mb-3">
                <div class="col-md-8">
                    <div class="d-flex align-items-end">
                        <div class="me-2">
                            <label for="start_date" class="form-label">Dari Tanggal:</label>
                            <input type="date" id="start_date" class="form-control form-control-sm">
                        </div>
                        <div class="me-2">
                            <label for="end_date" class="form-label">Sampai Tanggal:</label>
                            <input type="date" id="end_date" class="form-control form-control-sm">
                        </div>
                        <div>
                            <button id="filter-button" class="btn btn-primary btn-sm">Filter</button>
                            <button id="reset-filter-button" class="btn btn-secondary btn-sm ms-1">Reset</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert for showing last available data -->
            <div id="last-data-alert" class="alert alert-info mb-3" style="display: <?php echo e(isset($usedLastDate) && $usedLastDate ? 'block' : 'none'); ?>">
                <i class="mdi mdi-information-outline me-2"></i>
                <span>Tidak ada data untuk hari ini. Menampilkan data dari tanggal terakhir yang tersedia: <?php echo e(isset($lastDate) ? date('d F Y', strtotime($lastDate)) : ''); ?></span>
            </div>

            <table class="table table-hover w-100">
                <thead class="table-dark text-white">
                    <tr>
                        <th class="p-2">Site Yang Mengajukan</th>
                        <th class="p-2">Judul Pengajuan</th>
                        <th class="p-2">keterangan</th>
                        <th class="p-2">Tanggal</th>
                        <th class="p-2">Status</th>
                    </tr>
                </thead>
                <tbody id="requisitionList">
                    <?php $__currentLoopData = $requisitions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $req): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="requisition-row <?php if($req['status'] === 'diajukan'): ?> table-primary <?php elseif($req['status'] === 'pending'): ?> table-warning <?php endif; ?>"
                        data-id="<?php echo e($req['requisition_id']); ?>">
                        <td><?php echo e($req['site_name']); ?></td>
                        <td><?php echo e($req['title']); ?></td>
                        <td><?php echo e($req['notes']); ?></td>
                        <td><?php echo e($req['requisition_date']); ?></td>
                        <td><?php echo e(ucfirst($req['status'])); ?></td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
            <div id="requisitions-pagination" class="mt-3">
                <!-- Custom pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>
</div>
<script>
    // Default pagination data (will be overridden by JavaScript)
    window.initialPaginationData = {
        current_page: 1,
        per_page: 15,
        last_page: 1,
        total: 0
    };
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/confirmation.blade.php ENDPATH**/ ?>