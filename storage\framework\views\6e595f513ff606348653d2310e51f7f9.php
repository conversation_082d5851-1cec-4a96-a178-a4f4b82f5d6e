<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <title>Superadmin Login | Portal PWB</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/token-login.css']); ?>
    <style>
        body {
            background: url('<?php echo e(asset('assets/images/438463.png')); ?>');
        }
    </style>
</head>

<body>
    <div class="token-container">
        <div class="token-section">
            <div class="token-box">
                <h2>SUPERADMIN</h2>
                <p>Masukkan token untuk akses Superadmin</p>

                <?php if($errors->any()): ?>
                    <div class="alert alert-danger">
                        <ul>
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('token.login')); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <div class="input-group">
                        <i class="fas fa-key"></i>
                        <input type="text" id="token" name="token" required placeholder="Masukkan token">
                    </div>
                    <button type="submit">Masuk</button>
                </form>

                <div class="back-link">
                    <a href="<?php echo e(route('login')); ?>">
                        <i class="fas fa-arrow-left"></i> Kembali ke Login
                    </a>
                </div>
            </div>
        </div>

        <div class="chart-section">
            <img src="<?php echo e(asset('assets/images/6333220.png')); ?>" alt="Background Graphic">
        </div>
    </div>

    <div class="footer">
        <script>document.write(new Date().getFullYear())</script> &copy; Portal PWB
    </div>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/auth/token-login.blade.php ENDPATH**/ ?>