<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jasa_karyawan', function (Blueprint $table) {
            $table->id();
            $table->string('site_id', 50);
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->string('employee_id', 50);
            $table->foreign('employee_id')
                ->references('employee_id')
                ->on('users')
                ->onUpdate('cascade');
            $table->string('file_path')->nullable();
            $table->date('date');
            $table->decimal('amount', 15, 2);
            $table->enum('status', ['submitted', 'approved', 'rejected', 'done'])->default('submitted');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jasa_karyawan');
    }
};
