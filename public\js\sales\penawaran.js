document.addEventListener("DOMContentLoaded", function () {
    // Initialize variables
    let selectedParts = [];
    let isEditing = false;
    let searchTimeout = null;
    let customPartIdCounter = -1; // Negative IDs for custom parts to avoid conflicts with real part IDs

    // DOM elements
    const penawaranForm = document.getElementById("penawaran-form");
    const partSearch = document.getElementById("part-search");
    const partSuggestions = document.getElementById("part-suggestions");
    const partsTableBody = document.getElementById("parts-table-body");
    const grandTotal = document.getElementById("grand-total");
    const btnResetForm = document.getElementById("btn-reset-form");
    const btnShowForm = document.getElementById("btn-show-form");
    const btnCloseForm = document.getElementById("btn-close-form");
    const formContainer = document.getElementById("form-container");
    const tableContainer = document.getElementById("table-container");
    const monthFilter = document.getElementById("month-filter");
    const yearFilter = document.getElementById("year-filter");
    const editPenawaranId = document.getElementById("edit-penawaran-id");
    const customerInput = document.getElementById("customer");
    const customerSuggestions = document.getElementById("customer-suggestions");
    const btnAddCustomPart = document.getElementById("btn-add-custom-part");
    const customPartModal = new bootstrap.Modal(
        document.getElementById("custom-part-modal")
    );
    const invoiceModal = document.getElementById("invoiceModal")
        ? new bootstrap.Modal(document.getElementById("invoiceModal"))
        : null;
    const statusModal = document.getElementById("statusModal")
        ? new bootstrap.Modal(document.getElementById("statusModal"))
        : null;
    const btnSaveCustomPart = document.getElementById("btn-save-custom-part");
    const customPartForm = document.getElementById("custom-part-form");
    const customPartCode = document.getElementById("custom-part-code");
    const customPartName = document.getElementById("custom-part-name");
    const customPartType = document.getElementById("custom-part-type");
    const customPartPrice = document.getElementById("custom-part-price");
    const customPartPurchasePrice = document.getElementById("custom-part-purchase-price");
    const customPartEum = document.getElementById("custom-part-eum");
    const customPartQuantity = document.getElementById("custom-part-quantity");

    // Filter elements
    const btnShowFilter = document.getElementById("btn-show-filter");
    const filterContainer = document.getElementById("filter-container");
    const filterForm = document.getElementById("filter-form");
    const btnResetFilter = document.getElementById("btn-reset-filter");
    const dateFrom = document.getElementById("date-from");
    const dateTo = document.getElementById("date-to");
    const customerFilter = document.getElementById("customer-filter");

    // Event listeners
    btnShowForm.addEventListener("click", showForm);
    btnCloseForm.addEventListener("click", hideForm);
    btnAddCustomPart.addEventListener("click", showCustomPartModal);
    btnSaveCustomPart.addEventListener("click", addCustomPart);

    // Filter event listeners
    if (btnShowFilter) {
        btnShowFilter.addEventListener("click", toggleFilter);
    }

    if (filterForm) {
        filterForm.addEventListener("submit", applyFilter);
    }

    if (btnResetFilter) {
        btnResetFilter.addEventListener("click", resetFilter);
    }

    // Format custom part price inputs with Rupiah formatting
    function addCurrencyFormatting(inputElement) {
        inputElement.addEventListener("input", function () {
            // Store the current cursor position
            const cursorPos = this.selectionStart;
            // Get the raw value (numbers only)
            const rawValue = this.value.replace(/\D/g, "");
            // Store the original length
            const originalLength = this.value.length;

            if (rawValue) {
                // Format the value
                this.value = formatNumberAsRupiah(parseInt(rawValue, 10));

                // Calculate new cursor position
                const newLength = this.value.length;
                const newPos = cursorPos + (newLength - originalLength);

                // Set cursor position
                this.setSelectionRange(newPos, newPos);
            }
        });
    }

    // Apply currency formatting to both price fields
    addCurrencyFormatting(customPartPrice);
    addCurrencyFormatting(customPartPurchasePrice);

    // Customer autocomplete
    customerInput.addEventListener("input", function () {
        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set new timeout for search (300ms delay)
        searchTimeout = setTimeout(() => {
            const searchTerm = customerInput.value.trim();
            if (searchTerm.length >= 2) {
                searchCustomers(searchTerm);
            } else {
                customerSuggestions.style.display = "none";
            }
        }, 300);
    });

    // Hide customer suggestions when clicking outside and handle button clicks
    document.addEventListener("click", function (e) {
        // Hide customer suggestions
        if (
            !customerInput.contains(e.target) &&
            !customerSuggestions.contains(e.target)
        ) {
            customerSuggestions.style.display = "none";
        }

        // Handle invoice button clicks with event delegation
        if (e.target.closest(".btn-invoice-penawaran")) {
            e.preventDefault();
            const id = e.target.closest(".btn-invoice-penawaran").dataset.id;
            showInvoiceModal(id);
        }
    });

    // Month filter change event
    if (monthFilter) {
        monthFilter.addEventListener("change", function () {
            // Get the selected year from the data attribute of the selected option
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.getAttribute("data-year")) {
                yearFilter.value = selectedOption.getAttribute("data-year");
            }

            // If date range is selected, clear it when selecting a month
            if (this.value && dateFrom && dateTo) {
                dateFrom.value = "";
                dateTo.value = "";
            }
        });
    }
    partSearch.addEventListener("input", function () {
        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set new timeout for search (300ms delay)
        searchTimeout = setTimeout(() => {
            const searchTerm = partSearch.value.trim();
            if (searchTerm.length >= 2) {
                searchParts(searchTerm);
            } else {
                partSuggestions.style.display = "none";
            }
        }, 300);
    });

    // Hide suggestions when clicking outside
    document.addEventListener("click", function (e) {
        if (
            !partSearch.contains(e.target) &&
            !partSuggestions.contains(e.target)
        ) {
            partSuggestions.style.display = "none";
        }
    });

    penawaranForm.addEventListener("submit", savePenawaran);
    btnResetForm.addEventListener("click", resetForm);

    // Add event listeners for view, edit, and delete buttons
    document.querySelectorAll(".btn-view-penawaran").forEach((btn) => {
        btn.addEventListener("click", function () {
            const id = this.getAttribute("data-id");
            viewPenawaran(id);
        });
    });

    document.querySelectorAll(".btn-edit-penawaran").forEach((btn) => {
        btn.addEventListener("click", function () {
            const id = this.getAttribute("data-id");
            editPenawaran(id);
        });
    });

    document.querySelectorAll(".btn-delete-penawaran").forEach((btn) => {
        btn.addEventListener("click", function () {
            const id = this.getAttribute("data-id");
            deletePenawaran(id);
        });
    });

    // Add event listeners for invoice buttons
    document.querySelectorAll(".btn-invoice-penawaran").forEach((btn) => {
        btn.addEventListener("click", function () {
            const id = this.getAttribute("data-id");
            showInvoiceModal(id);
        });
    });

    // Add event listeners for status change buttons
    document.querySelectorAll(".btn-status-penawaran").forEach((btn) => {
        btn.addEventListener("click", function () {
            const id = this.getAttribute("data-id");
            const status = this.getAttribute("data-status");
            showStatusModal(id, status);
        });
    });

    // Functions
    function searchCustomers(searchTerm) {
        fetch(`/sales/customer-search?query=${encodeURIComponent(searchTerm)}`)
            .then((response) => response.json())
            .then((data) => {
                customerSuggestions.innerHTML = "";

                if (data.length === 0) {
                    customerSuggestions.innerHTML =
                        '<div class="list-group-item">Tidak ada customer yang ditemukan</div>';
                } else {
                    data.forEach((customer) => {
                        const item = document.createElement("a");
                        item.href = "#";
                        item.className =
                            "list-group-item list-group-item-action";
                        item.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${customer.code}</strong> - ${
                            customer.nama_customer
                        }
                                </div>
                            </div>
                            <div class="small text-muted">${
                                customer.alamat || ""
                            }</div>
                        `;

                        item.addEventListener("click", function (e) {
                            e.preventDefault();
                            customerInput.value = customer.nama_customer;

                            // Store customer code in a hidden field for later use
                            let customerCodeInput = document.getElementById("customer_code");
                            if (!customerCodeInput) {
                                customerCodeInput = document.createElement("input");
                                customerCodeInput.type = "hidden";
                                customerCodeInput.id = "customer_code";
                                customerCodeInput.name = "customer_code";
                                document.getElementById("penawaran-form").appendChild(customerCodeInput);
                            }
                            customerCodeInput.value = customer.code;

                            // If lokasi field is empty, fill it with customer's address
                            const lokasiInput =
                                document.getElementById("lokasi");
                            if (
                                lokasiInput &&
                                lokasiInput.value.trim() === "" &&
                                customer.alamat
                            ) {
                                lokasiInput.value = customer.alamat;
                            }

                            customerSuggestions.style.display = "none";
                        });

                        customerSuggestions.appendChild(item);
                    });
                }

                customerSuggestions.style.display = "block";
            })
            .catch((error) => {
                console.error("Error searching customers:", error);
                customerSuggestions.innerHTML =
                    '<div class="list-group-item text-danger">Error saat mencari customer</div>';
                customerSuggestions.style.display = "block";
            });
    }

    function searchParts(searchTerm) {
        fetch(
            `/sales/penawaran/search-parts?search=${encodeURIComponent(
                searchTerm
            )}`
        )
            .then((response) => response.json())
            .then((data) => {
                partSuggestions.innerHTML = "";

                if (data.length === 0) {
                    // Create a message that no parts were found
                    const noPartsMessage = document.createElement("div");
                    noPartsMessage.className = "list-group-item";
                    noPartsMessage.textContent =
                        "Tidak ada part yang ditemukan";
                    partSuggestions.appendChild(noPartsMessage);

                    // Add an option to create a new part
                    const addNewPartOption = document.createElement("a");
                    addNewPartOption.href = "#";
                    addNewPartOption.className =
                        "list-group-item list-group-item-action bg-primary text-white";
                    addNewPartOption.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="mdi mdi-plus-circle"></i> Tambah Part Baru: "${searchTerm}"
                            </div>
                        </div>
                    `;

                    addNewPartOption.addEventListener("click", function (e) {
                        e.preventDefault();
                        // Pre-fill the custom part form with the search term
                        customPartName.value = searchTerm;
                        customPartCode.value = generateCustomPartCode();
                        customPartType.value = "PERSEDIAAN LAINNYA"; // Default type
                        customPartPrice.value = "";
                        customPartPurchasePrice.value = "";
                        customPartEum.value = "EA";
                        customPartQuantity.value = "1";

                        // Show the modal
                        customPartModal.show();

                        // Hide suggestions
                        partSuggestions.style.display = "none";
                    });

                    partSuggestions.appendChild(addNewPartOption);
                } else {
                    data.forEach((part) => {
                        const item = document.createElement("a");
                        item.href = "#";
                        item.className =
                            "list-group-item list-group-item-action";
                        item.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${part.part_code}</strong> - ${
                            part.part_name
                        }
                                </div>
                                <span class="badge ${
                                    part.stock_quantity > 0
                                        ? "bg-success"
                                        : "bg-danger"
                                } rounded-pill">
                                    Stock: ${part.stock_quantity}
                                </span>
                            </div>
                        `;

                        item.addEventListener("click", function (e) {
                            e.preventDefault();
                            addPartToTable(part);
                            partSuggestions.style.display = "none";
                            partSearch.value = "";
                        });

                        partSuggestions.appendChild(item);
                    });
                }

                partSuggestions.style.display = "block";
            })
            .catch((error) => {
                console.error("Error searching parts:", error);
                partSuggestions.innerHTML =
                    '<div class="list-group-item text-danger">Error saat mencari part</div>';
                partSuggestions.style.display = "block";
            });
    }

    function addPartToTable(part) {
        // Check if part already exists in the table
        const existingPart = selectedParts.find(
            (p) => p.part_inventory_id === part.part_inventory_id
        );
        if (existingPart) {
            if (typeof Swal !== "undefined") {
                Swal.fire({
                    icon: "warning",
                    title: "Peringatan",
                    text: "Part ini sudah ada dalam daftar",
                });
            } else {
                alert("Part ini sudah ada dalam daftar");
            }
            return;
        }

        // Add part to selectedParts array
        const newPart = {
            part_inventory_id: part.part_inventory_id,
            part_code: part.part_code,
            part_name: part.part_name || part.site_part_name,
            quantity: 1,
            price: part.price || 0,
            stock_quantity: part.stock_quantity,
        };

        selectedParts.push(newPart);

        // Render the table
        renderPartsTable();
    }

    function renderPartsTable() {
        partsTableBody.innerHTML = "";
        let total = 0;

        selectedParts.forEach((part, index) => {
            const partTotal = part.quantity * part.price;
            total += partTotal;

            // Set default status if not present
            if (!part.status) {
                part.status = "Not Ready";
            }

            // Determine status badge class
            let statusClass = "";
            switch (part.status) {
                case "Ready":
                    statusClass = "bg-success";
                    break;
                case "In Order":
                    statusClass = "bg-info text-dark";
                    break;
                case "Not Ready":
                    statusClass = "bg-danger";
                    break;
                default:
                    statusClass = "bg-secondary";
            }

            // Create status badge HTML
            const statusBadge = isEditing
                ? `<span class="badge ${statusClass} text-white">${part.status}</span>`
                : "";

            const row = document.createElement("tr");
            row.innerHTML = `
                <td>
                    <div class="small fw-bold">${part.part_code}</div>
                    <div class="small text-muted">${part.part_name}</div>
                    ${isEditing ? `<div class="mt-1">${statusBadge}</div>` : ""}
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm part-quantity"
                           data-index="${index}" value="${
                part.quantity
            }" min="1">
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm part-price"
                           data-index="${index}" value="${formatNumberAsRupiah(
                part.price
            )}"
                           data-original-value="${part.price}">
                </td>
                <td class="part-total">${formatCurrency(partTotal)}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger btn-remove-part" data-index="${index}">
                        <i class="mdi mdi-delete"></i>
                    </button>
                </td>
            `;

            partsTableBody.appendChild(row);
        });

        // Update grand total
        grandTotal.textContent = formatCurrency(total);

        // Add event listeners to quantity inputs
        document.querySelectorAll(".part-quantity").forEach((input) => {
            input.addEventListener("change", updatePartTotal);
        });

        // Add event listeners to price inputs with Rupiah formatting
        document.querySelectorAll(".part-price").forEach((input) => {
            // Format on focus
            input.addEventListener("focus", function () {
                // Store the current position
                const cursorPos = this.selectionStart;
                // Get the raw number value
                const rawValue = parseRupiahString(this.value);
                // Update with raw value for easier editing
                this.value = rawValue;
                // Restore cursor position
                this.setSelectionRange(cursorPos, cursorPos);
            });

            // Format on input
            input.addEventListener("input", function () {
                // Store the current cursor position
                const cursorPos = this.selectionStart;
                // Get the raw value (numbers only)
                const rawValue = this.value.replace(/\D/g, "");
                // Store the original length
                const originalLength = this.value.length;

                if (rawValue) {
                    // Format the value
                    this.value = formatNumberAsRupiah(parseInt(rawValue, 10));

                    // Calculate new cursor position
                    const newLength = this.value.length;
                    const newPos = cursorPos + (newLength - originalLength);

                    // Set cursor position
                    this.setSelectionRange(newPos, newPos);
                }
            });

            // Update part total on blur
            input.addEventListener("blur", function () {
                // Update the data-original-value attribute
                this.setAttribute(
                    "data-original-value",
                    parseRupiahString(this.value)
                );
                // Call the update function
                updatePartTotal.call(this);
            });
        });

        // Add event listeners to remove buttons
        document.querySelectorAll(".btn-remove-part").forEach((btn) => {
            btn.addEventListener("click", removePart);
        });
    }

    function updatePartTotal() {
        const index = parseInt(this.getAttribute("data-index"));
        const isQuantity = this.classList.contains("part-quantity");

        if (isQuantity) {
            selectedParts[index].quantity = parseInt(this.value) || 1;
        } else {
            // For price inputs, use the data-original-value attribute or parse the Rupiah formatted string
            const priceValue = this.hasAttribute("data-original-value")
                ? parseFloat(this.getAttribute("data-original-value"))
                : parseRupiahString(this.value);

            selectedParts[index].price = priceValue || 0;
        }

        renderPartsTable();
    }

    function removePart() {
        const index = parseInt(this.getAttribute("data-index"));
        selectedParts.splice(index, 1);
        renderPartsTable();
    }

    function savePenawaran(e) {
        e.preventDefault();

        if (selectedParts.length === 0) {
            if (typeof Swal !== "undefined") {
                Swal.fire({
                    icon: "warning",
                    title: "Peringatan",
                    text: "Tambahkan minimal satu part ke penawaran",
                });
            } else {
                alert("Tambahkan minimal satu part ke penawaran");
            }
            return;
        }

        // Prepare form data
        const formData = {
            nomor: document.getElementById("nomor").value,
            tanggal_penawaran:
                document.getElementById("tanggal_penawaran").value,
            perihal: document.getElementById("perihal").value,
            customer: document.getElementById("customer").value,
            attn: document.getElementById("attn").value,
            lokasi: document.getElementById("lokasi").value,
            notes: document.getElementById("notes").value,
            diskon: document.getElementById("diskon").value,
            status: isEditing
                ? document.getElementById("status").value
                : "Draft",
        };

        // Add customer code if available
        const customerCodeInput = document.getElementById("customer_code");
        if (customerCodeInput && customerCodeInput.value) {
            formData.customer_code = customerCodeInput.value;
        }

        // Add parts data
        formData.parts = selectedParts.map((part) => {
            // Check if this is a custom part by its negative ID
            const isCustomPart =
                part.is_custom ||
                    part.part_inventory_id < 0 ||
                    part.part_code.startsWith("CUSTOM-");

                const partData = {
                    part_inventory_id: part.part_inventory_id,
                    quantity: part.quantity,
                    price: part.price,
                    status: part.status || "Not Ready",
                    is_custom: isCustomPart, // Set is_custom based on our check
                    part_code: part.part_code,
                    part_name: part.part_name,
                };

                // Add additional fields for custom parts
                if (isCustomPart) {
                    partData.part_type = part.part_type || 'PERSEDIAAN LAINNYA';
                    partData.purchase_price = part.purchase_price || 0;
                    partData.eum = part.eum || 'EA';

                    // Debug logging for custom parts
                    console.log("Custom part data being sent:", {
                        part_code: partData.part_code,
                        part_name: partData.part_name,
                        part_type: partData.part_type,
                        price: partData.price,
                        purchase_price: partData.purchase_price,
                        eum: partData.eum,
                        is_custom: partData.is_custom
                    });
                }

                return partData;
            }),
        };

        // Determine if we're creating or updating
        const url = isEditing
            ? `/sales/penawaran/${editPenawaranId.value}`
            : "/sales/penawaran";

        const method = isEditing ? "PUT" : "POST";

        // Send request
        fetch(url, {
            method: method,
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify(formData),
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil",
                        text: data.message,
                        confirmButtonText: "OK",
                    }).then(() => {
                        // Reload the page to show updated data
                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: data.message,
                    });
                }
            })
            .catch((error) => {
                console.error("Error saving penawaran:", error);
                // Check if Swal is defined before using it
                if (typeof Swal !== "undefined") {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Terjadi kesalahan saat menyimpan penawaran",
                    });
                } else {
                    alert("Terjadi kesalahan saat menyimpan penawaran");
                }
            });
    }

    function viewPenawaran(id) {
        fetch(`/sales/penawaran/${id}`)
            .then((response) => response.json())
            .then((data) => {
                // Fill modal with penawaran data
                document.getElementById("view-nomor").textContent = data.nomor;
                document.getElementById("view-perihal").textContent =
                    data.perihal;
                document.getElementById("view-customer").textContent =
                    data.customer;
                document.getElementById("view-attn").textContent =
                    data.attn || "-";
                document.getElementById("view-lokasi").textContent =
                    data.lokasi;
                // Use tanggal_penawaran if available, otherwise fall back to created_at
                let dateToDisplay = data.created_at;
                if (data.tanggal_penawaran) {
                    dateToDisplay = data.tanggal_penawaran;
                }
                document.getElementById("view-tanggal").textContent =
                    formatDate(dateToDisplay);

                // Display status with badge
                const statusElement = document.getElementById("view-status");
                let statusClass = "";
                switch (data.status) {
                    case "Draft":
                        statusClass = "bg-secondary";
                        break;
                    case "Dikirim ke customer":
                        statusClass = "bg-info";
                        break;
                    case "PO customer":
                        statusClass = "bg-primary";
                        break;
                    case "Proses penyediaan":
                        statusClass = "bg-info text-dark";
                        break;
                    case "Selesai":
                        statusClass = "bg-success";
                        break;
                    default:
                        statusClass = "bg-secondary";
                }
                statusElement.innerHTML = `<span class="badge ${statusClass} text-white">${
                    data.status || "Draft"
                }</span>`;

                // Format notes as unordered list if it contains line breaks
                const notesElement = document.getElementById("view-notes");
                if (data.notes && data.notes.includes("\n")) {
                    const notesList = data.notes
                        .split("\n")
                        .filter((note) => note.trim() !== "");
                    notesElement.innerHTML =
                        "<ul>" +
                        notesList.map((note) => `<li>${note}</li>`).join("") +
                        "</ul>";
                } else {
                    notesElement.textContent = data.notes || "-";
                }

                // Fill parts table
                const partsTableBody = document.getElementById(
                    "view-parts-table-body"
                );
                partsTableBody.innerHTML = "";
                let total = 0;

                data.items.forEach((item) => {
                    const partTotal = item.quantity * item.price;
                    total += partTotal;

                    const row = document.createElement("tr");
                    row.innerHTML = `
                        <td>
                            <div class="small fw-bold">${
                                item.part_inventory.part_code
                            }</div>
                            <div class="small text-muted">${
                                item.part_inventory.part.part_name
                            }</div>
                        </td>
                        <td>${item.quantity}</td>
                        <td>${formatCurrency(item.price)}</td>
                        <td>${formatCurrency(partTotal)}</td>
                    `;

                    partsTableBody.appendChild(row);
                });

                // Update grand total
                document.getElementById("view-grand-total").textContent =
                    formatCurrency(total);
                document
                    .getElementById("view-grand-total")
                    .classList.add("text-nowrap");

                // Set PDF link
                document.getElementById(
                    "view-pdf-link"
                ).href = `/sales/penawaran/${id}/pdf`;

                // Set Invoice link
                document.getElementById(
                    "view-invoice-link"
                ).href = `/sales/penawaran/${id}/invoice`;

                // Show modal
                const modal = new bootstrap.Modal(
                    document.getElementById("view-penawaran-modal")
                );
                modal.show();
            })
            .catch((error) => {
                console.error("Error viewing penawaran:", error);
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Terjadi kesalahan saat memuat data penawaran",
                });
            });
    }

    function editPenawaran(id) {
        fetch(`/sales/penawaran/${id}`)
            .then((response) => response.json())
            .then((data) => {
                // Set form to edit mode
                isEditing = true;
                editPenawaranId.value = id;

                // Fill form with penawaran data
                document.getElementById("nomor").value = data.nomor;

                // Set tanggal_penawaran if available, otherwise use created_at or today's date
                try {
                    let dateToUse;
                    if (data.tanggal_penawaran) {
                        dateToUse = new Date(data.tanggal_penawaran);
                    } else if (data.created_at) {
                        dateToUse = new Date(data.created_at);
                    } else {
                        dateToUse = new Date();
                    }

                    // Format date as YYYY-MM-DD for the date input
                    const formattedDate = dateToUse.toISOString().split("T")[0];
                    document.getElementById("tanggal_penawaran").value =
                        formattedDate;
                } catch (error) {
                    console.error("Error formatting date:", error);
                    // Fallback to today's date if there's an error
                    const today = new Date();
                    const formattedToday = today.toISOString().split("T")[0];
                    document.getElementById("tanggal_penawaran").value =
                        formattedToday;
                }

                document.getElementById("perihal").value = data.perihal;
                document.getElementById("customer").value = data.customer;
                document.getElementById("attn").value = data.attn || "";
                document.getElementById("lokasi").value = data.lokasi;
                document.getElementById("notes").value = data.notes;
                document.getElementById("diskon").value = data.diskon || 0;

                // Try to find and set customer code if customer exists in customer_sales
                if (data.customer) {
                    fetch(`/sales/customer-search?query=${encodeURIComponent(data.customer)}`)
                        .then(response => response.json())
                        .then(customers => {
                            const matchingCustomer = customers.find(c => c.nama_customer === data.customer);
                            if (matchingCustomer) {
                                let customerCodeInput = document.getElementById("customer_code");
                                if (!customerCodeInput) {
                                    customerCodeInput = document.createElement("input");
                                    customerCodeInput.type = "hidden";
                                    customerCodeInput.id = "customer_code";
                                    customerCodeInput.name = "customer_code";
                                    document.getElementById("penawaran-form").appendChild(customerCodeInput);
                                }
                                customerCodeInput.value = matchingCustomer.code;
                            }
                        })
                        .catch(error => console.error('Error finding customer code:', error));
                }

                // Show and set status field
                const statusContainer =
                    document.getElementById("status-container");
                statusContainer.style.display = "block";
                document.getElementById("status").value =
                    data.status || "Draft";

                // Clear and fill parts table
                selectedParts = [];
                data.items.forEach((item) => {
                    // Check if this is a custom part (part code starts with CUSTOM- or has a negative ID)
                    const isCustomPart =
                        item.part_inventory.part_code.startsWith("CUSTOM-") ||
                        item.part_inventory_id < 0;

                    const partData = {
                        part_inventory_id: item.part_inventory_id,
                        part_code: item.part_inventory.part_code,
                        part_name: item.part_inventory.part.part_name,
                        quantity: item.quantity,
                        price: item.price,
                        stock_quantity: item.part_inventory.stock_quantity,
                        status: item.status || "Not Ready",
                        is_custom: isCustomPart, // Set is_custom flag based on part code or ID
                    };

                    // Add additional fields for custom parts
                    if (isCustomPart && item.part_inventory.part) {
                        partData.part_type = item.part_inventory.part.part_type || 'PERSEDIAAN LAINNYA';
                        partData.purchase_price = item.part_inventory.part.purchase_price || 0;
                        partData.eum = item.part_inventory.part.eum || 'EA';
                    }

                    selectedParts.push(partData);
                });

                renderPartsTable();

                // Show form
                showForm();
            })
            .catch((error) => {
                console.error("Error editing penawaran:", error);
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Terjadi kesalahan saat memuat data penawaran untuk diedit",
                });
            });
    }

    function deletePenawaran(id) {
        if (typeof Swal !== "undefined") {
            Swal.fire({
                title: "Konfirmasi",
                text: "Apakah Anda yakin ingin menghapus penawaran ini?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Ya, Hapus!",
                cancelButtonText: "Batal",
            }).then((result) => {
                if (result.isConfirmed) {
                    performDelete(id);
                }
            });
        } else {
            if (confirm("Apakah Anda yakin ingin menghapus penawaran ini?")) {
                performDelete(id);
            }
        }
    }

    function performDelete(id) {
        fetch(`/sales/penawaran/${id}`, {
            method: "DELETE",
            headers: {
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    if (typeof Swal !== "undefined") {
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil",
                            text: data.message,
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        alert(data.message);
                        window.location.reload();
                    }
                } else {
                    if (typeof Swal !== "undefined") {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: data.message,
                        });
                    } else {
                        alert("Error: " + data.message);
                    }
                }
            })
            .catch((error) => {
                console.error("Error deleting penawaran:", error);
                if (typeof Swal !== "undefined") {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Terjadi kesalahan saat menghapus penawaran",
                    });
                } else {
                    alert("Terjadi kesalahan saat menghapus penawaran");
                }
            });
    }

    function resetForm() {
        isEditing = false;
        editPenawaranId.value = "";
        penawaranForm.reset();
        selectedParts = [];
        renderPartsTable();

        // Clear customer code input if it exists
        const customerCodeInput = document.getElementById("customer_code");
        if (customerCodeInput) {
            customerCodeInput.remove();
        }

        // Hide status container when resetting form
        const statusContainer = document.getElementById("status-container");
        if (statusContainer) {
            statusContainer.style.display = "none";
        }
    }

    // Form visibility functions
    function showForm() {
        formContainer.style.display = "block";

        // Make table container smaller on larger screens
        if (window.innerWidth >= 768) {
            tableContainer.classList.remove("col-md-12");
            tableContainer.classList.add("col-md-7");
        }

        // If not in edit mode, fetch and suggest the next penawaran number
        if (!isEditing) {
            fetchNextPenawaranNumber();
        }

        // Scroll to form
        formContainer.scrollIntoView({ behavior: "smooth" });
    }

    // Function to fetch the next penawaran number
    function fetchNextPenawaranNumber() {
        fetch("/sales/penawaran/next-number")
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    // Set the suggested number in the nomor field
                    document.getElementById("nomor").value = data.next_number;
                }
            })
            .catch((error) => {
                console.error("Error fetching next penawaran number:", error);
            });
    }

    function hideForm() {
        formContainer.style.display = "none";

        // Make table container full width
        tableContainer.classList.remove("col-md-7");
        tableContainer.classList.add("col-md-12");

        resetForm();
    }

    // Handle responsive layout on window resize
    window.addEventListener("resize", function () {
        if (formContainer.style.display === "none") {
            // If form is hidden, make table full width
            tableContainer.classList.remove("col-md-7");
            tableContainer.classList.add("col-md-12");
        } else if (window.innerWidth >= 768) {
            // If form is visible and screen is large enough, split the layout
            tableContainer.classList.remove("col-md-12");
            tableContainer.classList.add("col-md-7");
        }
    });

    // Initialize responsive layout on page load
    if (formContainer.style.display === "none") {
        tableContainer.classList.remove("col-md-7");
        tableContainer.classList.add("col-md-12");
    }

    // Custom part functions
    function showCustomPartModal() {
        // Reset form
        customPartForm.reset();

        // Set default values
        customPartCode.value = generateCustomPartCode();
        customPartEum.value = 'EA';
        customPartQuantity.value = '1';
        customPartPrice.value = '';
        customPartPurchasePrice.value = '';

        // Show modal
        customPartModal.show();
    }

    function generateCustomPartCode() {
        // Generate a unique code for custom parts
        // Format: CUSTOM-YYYY-MM-DD-XXXX where XXXX is a random number
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, "0");
        const day = String(now.getDate()).padStart(2, "0");
        const random = Math.floor(Math.random() * 10000)
            .toString()
            .padStart(4, "0");

        return `CUSTOM-${year}${month}${day}-${random}`;
    }

    function addCustomPart() {
        // Validate required fields
        if (!customPartCode.value || !customPartName.value || !customPartType.value || !customPartPrice.value) {
            Swal.fire({
                icon: "error",
                title: "Error",
                text: "Kode part, nama part, tipe part, dan harga jual harus diisi",
            });
            return;
        }

        // Get values from form
        const partCode = customPartCode.value.trim();
        const partName = customPartName.value.trim();
        const partType = customPartType.value;
        const price = parseRupiahString(customPartPrice.value);
        const purchasePrice = parseRupiahString(customPartPurchasePrice.value) || 0;
        const eum = customPartEum.value.trim() || 'EA';
        const quantity = parseInt(customPartQuantity.value) || 1;

        // Debug logging
        console.log("Custom part form values:", {
            partCode,
            partName,
            partType,
            price,
            purchasePrice,
            eum,
            quantity,
            rawPurchasePrice: customPartPurchasePrice.value
        });

        // Validate price
        if (price <= 0) {
            Swal.fire({
                icon: "error",
                title: "Error",
                text: "Harga jual harus lebih dari 0",
            });
            return;
        }

        // Create custom part object for the quotation table
        const customPart = {
            part_inventory_id: customPartIdCounter--, // Use negative ID to indicate custom part
            part_code: partCode,
            part_name: partName,
            part_type: partType,
            quantity: quantity,
            price: price,
            purchase_price: purchasePrice,
            eum: eum,
            stock_quantity: 0,
            is_custom: true, // Flag to indicate this is a custom part - IMPORTANT: must be boolean true, not string
        };

        // Log the custom part for debugging
        console.log("Adding custom part:", customPart);

        // Add to selected parts
        addPartToTable(customPart);

        // Hide modal
        customPartModal.hide();

        // Clear form and reset defaults
        customPartForm.reset();
        customPartEum.value = 'EA'; // Reset EUM to default
        customPartQuantity.value = '1'; // Reset quantity to default
    }

    // Helper functions
    function formatCurrency(amount) {
        return new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
        }).format(amount);
    }

    // Format number as Rupiah without currency symbol
    function formatNumberAsRupiah(number) {
        return new Intl.NumberFormat("id-ID", {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(number);
    }

    // Parse Rupiah formatted string back to number
    function parseRupiahString(rupiahString) {
        if (!rupiahString) return 0;
        return parseInt(rupiahString.replace(/\./g, ""), 10) || 0;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat("id-ID", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
        }).format(date);
    }

    // Invoice functions
    function showInvoiceModal(id) {
        console.log("showInvoiceModal called with ID:", id);

        // Reset form
        document.getElementById("invoiceForm").reset();

        // Set penawaran ID
        document.getElementById("penawaran_id").value = id;

        // Fetch invoice data if exists
        fetch(`/sales/penawaran/${id}/invoice-data`)
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    // Set penawaran data
                    const penawaran = data.penawaran;

                    // If invoice exists, fill form with invoice data
                    if (data.has_invoice) {
                        const invoice = data.invoice;
                        document.getElementById("invoice_id").value =
                            invoice.id;
                        document.getElementById("model_unit").value =
                            invoice.model_unit || "";
                        document.getElementById("hmkm").value =
                            invoice.hmkm || "";
                        document.getElementById("sn").value = invoice.sn || "";
                        document.getElementById("trouble").value =
                            invoice.trouble || "";
                        document.getElementById("invoice_lokasi").value =
                            invoice.lokasi ||
                            invoice.location ||
                            penawaran.lokasi ||
                            "";
                        document.getElementById("transfer_to").value =
                            invoice.transfer_to || "PT.PUTERA WIBOWO BORNEO";
                        document.getElementById("bank_account").value =
                            invoice.bank_account || "0623-01-001201-0";
                        document.getElementById("bank_branch").value =
                            invoice.bank_branch ||
                            "Bank BRI, Cabang Banjarmasin A Yani";
                        document.getElementById("notes").value =
                            invoice.notes || "";
                        document.getElementById("no_invoice").value =
                            invoice.no_invoice || "";
                        document.getElementById("po_number").value =
                            invoice.po_number || "";

                        // Set invoice date if available
                        if (invoice.tanggal_invoice) {
                            // Format date as YYYY-MM-DD for input[type="date"]
                            const invoiceDate = new Date(
                                invoice.tanggal_invoice
                            );
                            const formattedDate = invoiceDate
                                .toISOString()
                                .split("T")[0];
                            document.getElementById("tanggal_invoice").value =
                                formattedDate;
                        }

                        // Set due date if available
                        if (invoice.due_date) {
                            const dueDate = new Date(invoice.due_date);
                            const formattedDueDate = dueDate
                                .toISOString()
                                .split("T")[0];
                            document.getElementById("due_date").value =
                                formattedDueDate;
                        }
                        // Set print invoice link
                        document.getElementById(
                            "printInvoice"
                        ).href = `/sales/penawaran/${id}/invoice`;
                        document.getElementById("printInvoice").style.display =
                            "inline-block";
                    } else {
                        // New invoice, set default values
                        document.getElementById("invoice_id").value = "";
                        document.getElementById("model_unit").value = "";
                        document.getElementById("hmkm").value = "";
                        document.getElementById("sn").value = "";
                        document.getElementById("trouble").value = "";
                        document.getElementById("invoice_lokasi").value =
                            penawaran.lokasi || "";
                        document.getElementById("transfer_to").value =
                            "PT.PUTERA WIBOWO BORNEO";
                        document.getElementById("bank_account").value =
                            "0623-01-001201-30-0";
                        document.getElementById("bank_branch").value =
                            "Bank BRI, Cabang Banjarmasin A Yani";
                        document.getElementById("notes").value = "";
                        document.getElementById("no_invoice").value = "";
                        document.getElementById("po_number").value = "";

                        // Set today's date as default for new invoice
                        const today = new Date();
                        const formattedToday = today
                            .toISOString()
                            .split("T")[0];
                        document.getElementById("tanggal_invoice").value =
                            formattedToday;

                        // Hide print invoice link for new invoice
                        document.getElementById("printInvoice").style.display =
                            "none";
                    }

                    // Show modal
                    if (invoiceModal) {
                        invoiceModal.show();
                    } else {
                        // If modal not initialized yet, initialize it
                        const modal = new bootstrap.Modal(
                            document.getElementById("invoiceModal")
                        );
                        modal.show();
                    }
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text:
                            data.message ||
                            "Terjadi kesalahan saat memuat data invoice",
                    });
                }
            })
            .catch((error) => {
                console.error("Error fetching invoice data:", error);
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Terjadi kesalahan saat memuat data invoice",
                });
            });
    }

    // Save invoice
    document
        .getElementById("saveInvoice")
        .addEventListener("click", function () {
            // Get form data
            const formData = new FormData(
                document.getElementById("invoiceForm")
            );

            // Convert to JSON
            const jsonData = {};
            formData.forEach((value, key) => {
                jsonData[key] = value;
            });

            // Save invoice
            fetch("/sales/penawaran/save-invoice", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
                body: JSON.stringify(jsonData),
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil",
                            text: data.message || "Invoice berhasil disimpan",
                        }).then(() => {
                            // Update print invoice link
                            document.getElementById("printInvoice").href =
                                data.invoice_url;
                            document.getElementById(
                                "printInvoice"
                            ).style.display = "inline-block";

                            // Update invoice ID
                            document.getElementById("invoice_id").value =
                                data.invoice.id;
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text:
                                data.message ||
                                "Terjadi kesalahan saat menyimpan invoice",
                        });
                    }
                })
                .catch((error) => {
                    console.error("Error saving invoice:", error);
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Terjadi kesalahan saat menyimpan invoice",
                    });
                });
        });

    // Function to show status change modal
    function showStatusModal(id, currentStatus) {
        // Reset form
        document.getElementById("statusForm").reset();

        // Set penawaran ID and current status
        document.getElementById("status_penawaran_id").value = id;
        document.getElementById("current_status").value = currentStatus;

        // Set current status in dropdown
        const newStatusSelect = document.getElementById("new_status");
        newStatusSelect.value = currentStatus;

        // Show/hide invoice status section based on selected status
        toggleInvoiceStatusSection(newStatusSelect.value);

        // Add event listener to status dropdown
        newStatusSelect.addEventListener("change", function () {
            toggleInvoiceStatusSection(this.value);
        });

        // Initialize dropify for file uploads
        try {
            if ($.fn.dropify) {
                $(".dropify").dropify({
                    messages: {
                        default: "Seret dan lepas file di sini atau klik",
                        replace: "Seret dan lepas atau klik untuk mengganti",
                        remove: "Hapus",
                        error: "Oops, terjadi kesalahan.",
                    },
                    error: {
                        fileSize: "Ukuran file terlalu besar (maks 10MB).",
                    },
                });
            }
        } catch (e) {
            console.error("Error initializing dropify:", e);
        }

        // Show modal
        if (statusModal) {
            statusModal.show();
        } else {
            // If modal not initialized yet, initialize it
            const modal = new bootstrap.Modal(
                document.getElementById("statusModal")
            );
            modal.show();
        }
    }

    // Function to toggle invoice status section visibility
    function toggleInvoiceStatusSection(status) {
        const invoiceStatusSection = document.getElementById(
            "invoice_status_section"
        );
        if (status === "Selesai") {
            invoiceStatusSection.style.display = "block";
        } else {
            invoiceStatusSection.style.display = "none";
        }
    }

    // Save status change
    document
        .getElementById("saveStatus")
        .addEventListener("click", function () {
            // Get form data
            const formData = new FormData(
                document.getElementById("statusForm")
            );

            // Send request
            fetch("/sales/penawaran/update-status", {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
                body: formData,
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil",
                            text: data.message || "Status berhasil diperbarui",
                        }).then(() => {
                            // Reload the page to show updated data
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text:
                                data.message ||
                                "Terjadi kesalahan saat memperbarui status",
                        });
                    }
                })
                .catch((error) => {
                    console.error("Error updating status:", error);
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Terjadi kesalahan saat memperbarui status",
                    });
                });
        });

    // Filter functions
    function toggleFilter() {
        if (
            filterContainer.style.display === "none" ||
            !filterContainer.style.display
        ) {
            filterContainer.style.display = "block";
        } else {
            filterContainer.style.display = "none";
        }
    }

    function applyFilter(e) {
        e.preventDefault();

        // Create URL with filter parameters
        const params = new URLSearchParams();

        // Add date range parameters if both dates are provided
        if (dateFrom.value && dateTo.value) {
            params.append("date_from", dateFrom.value);
            params.append("date_to", dateTo.value);
        }

        // Add customer filter if provided
        if (customerFilter.value) {
            params.append("customer", customerFilter.value);
        }

        // Add month and year if selected and date range is not provided
        if (
            monthFilter.value &&
            yearFilter.value &&
            (!dateFrom.value || !dateTo.value)
        ) {
            params.append("month", monthFilter.value);
            params.append("year", yearFilter.value);
        }

        // Redirect to the same page with filter parameters
        window.location.href = `${
            window.location.pathname
        }?${params.toString()}`;
    }

    function resetFilter() {
        // Reset all filter inputs
        if (dateFrom) dateFrom.value = "";
        if (dateTo) dateTo.value = "";
        if (customerFilter) customerFilter.value = "";
        if (monthFilter) monthFilter.value = "";
        if (yearFilter) yearFilter.value = "";

        // Redirect to the page without parameters
        window.location.href = window.location.pathname;
    }
});
