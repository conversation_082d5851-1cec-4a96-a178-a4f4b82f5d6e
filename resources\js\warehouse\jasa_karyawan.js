$(document).ready(function() {
    // Load initial data
    loadJasaKaryawanData();
    $('#add-jasa-karyawan-btn').click(function() {
        resetForm();
        $('#jasa-karyawan-modal-label').text('tambah Monthly Report');

        // Try both Bootstrap 4 and 5 modal methods
        try {
            var jasaKaryawanModal = new bootstrap.Modal(document.getElementById('jasa-karyawan-modal'));
            jasaKaryawanModal.show();
        } catch (e) {
            $('#jasa-karyawan-modal').modal('show');
        }
    });
    $('#save-jasa-karyawan-btn').click(function() {
        saveJasaKaryawan();
    });

    // Filter button click
    $('#filter-btn').click(function() {
        loadJasaKaryawanData();
    });

    // Reset filter button click
    $('#reset-filter-btn').click(function() {
        $('#search-input').val('');
        $('#status-filter').val('');
        $('#start-date').val('');
        $('#end-date').val('');
        loadJasaKaryawanData();
    });

    // Handle pagination click (delegated event)
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const page = $(this).attr('href').split('page=')[1];
        loadJasaKaryawanData(page);
    });
});

// Function to loadMonthly Reportdata
function loadJasaKaryawanData(page = 1) {
    const search = $('#search-input').val();
    const status = $('#status-filter').val();
    const startDate = $('#start-date').val();
    const endDate = $('#end-date').val();

    $.ajax({
        url: '/warehouse/jasa-karyawan/data',
        type: 'GET',
        data: {
            page: page,
            search: search,
            status: status,
            start_date: startDate,
            end_date: endDate,
            per_page: 5
        },
        beforeSend: function() {
            // Show loading indicator
            $('#jasa-karyawan-table tbody').html('<tr><td colspan="7" class="text-center">Loading...</td></tr>');
        },
        success: function(response) {
            renderTable(response);
            renderPagination(response);
        },
        error: function(xhr) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Gagal memuat data. Silakan coba lagi.'
            });
        }
    });
}

// Function to render table data
function renderTable(response) {
    const data = response.data;
    let html = '';

    if (data.length === 0) {
        html = '<tr><td colspan="7" class="text-center">Tidak ada data</td></tr>';
    } else {
        data.forEach((item, index) => {
            const date = new Date(item.date).toLocaleDateString('id-ID', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });

            let statusBadge = '';
            switch (item.status) {
                case 'submitted':
                    statusBadge = '<span class="badge bg-secondary">Diajukan</span>';
                    break;
                case 'approved':
                    statusBadge = '<span class="badge bg-primary">Disetujui</span>';
                    break;
                case 'rejected':
                    statusBadge = '<span class="badge bg-danger">Ditolak</span>';
                    break;
                case 'done':
                    statusBadge = '<span class="badge bg-success">Selesai</span>';
                    break;
                default:
                    statusBadge = '<span class="badge bg-secondary">Unknown</span>';
            }

            const fileButton = item.file_path
                ? `<button class="btn btn-sm btn-info view-file-btn" data-id="${item.id}"><i class="mdi mdi-file-document"></i> Lihat</button>`
                : '<span class="text-muted">Tidak ada file</span>';

            const editButton = item.status !== 'done'
                ? `<button class="btn btn-sm btn-primary edit-btn" data-id="${item.id}"><i class="mdi mdi-pencil"></i></button>`
                : '';

            const deleteButton = item.status !== 'done'
                ? `<button class="btn btn-sm btn-danger delete-btn" data-id="${item.id}"><i class="mdi mdi-trash-can"></i></button>`
                : '';

            html += `
                <tr>
                    <td>${response.from + index}</td>
                    <td>${date}</td>
                    <td>Rp ${new Intl.NumberFormat('id-ID').format(item.amount)}</td>
                    <td>${statusBadge}</td>
                    <td>${item.notes || '-'}</td>
                    <td>${fileButton}</td>
                    <td>
                        ${editButton}
                        ${deleteButton}
                    </td>
                </tr>
            `;
        });
    }

    $('#jasa-karyawan-table tbody').html(html);

    // Attach event handlers to the newly created buttons
    $('.edit-btn').click(function() {
        const id = $(this).data('id');
        editJasaKaryawan(id);
    });

    $('.delete-btn').click(function() {
        const id = $(this).data('id');
        deleteJasaKaryawan(id);
    });

    $('.view-file-btn').click(function() {
        const id = $(this).data('id');
        viewFile(id);
    });
}

// Function to render pagination
function renderPagination(response) {
    let html = '';

    if (response.last_page > 1) {
        html = '<ul class="pagination pagination-rounded justify-content-end mb-0">';

        // Previous page link
        if (response.current_page > 1) {
            html += `<li class="page-item"><a class="page-link" href="?page=${response.current_page - 1}">&laquo;</a></li>`;
        } else {
            html += '<li class="page-item disabled"><span class="page-link">&laquo;</span></li>';
        }

        // Page links
        let startPage = Math.max(1, response.current_page - 2);
        let endPage = Math.min(response.last_page, response.current_page + 2);

        if (startPage > 1) {
            html += '<li class="page-item"><a class="page-link" href="?page=1">1</a></li>';
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            if (i === response.current_page) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                html += `<li class="page-item"><a class="page-link" href="?page=${i}">${i}</a></li>`;
            }
        }

        if (endPage < response.last_page) {
            if (endPage < response.last_page - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += `<li class="page-item"><a class="page-link" href="?page=${response.last_page}">${response.last_page}</a></li>`;
        }

        // Next page link
        if (response.current_page < response.last_page) {
            html += `<li class="page-item"><a class="page-link" href="?page=${response.current_page + 1}">&raquo;</a></li>`;
        } else {
            html += '<li class="page-item disabled"><span class="page-link">&raquo;</span></li>';
        }

        html += '</ul>';
    }

    $('.pagination-container').html(html);
}

// Function to saveMonthly Report
function saveJasaKaryawan() {
    const form = $('#jasa-karyawan-form')[0];
    const formData = new FormData(form);
    const id = $('#jasa-karyawan-id').val();

    // Determine if this is an update or create operation
    const url = id ? `/warehouse/jasa-karyawan/${id}` : '/warehouse/jasa-karyawan';
    const method = id ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        type: method,
        data: formData,
        processData: false,
        contentType: false,
        beforeSend: function() {
            // Disable the save button and show loading state
            $('#save-jasa-karyawan-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
        },
        success: function(response) {
            // Close the modal and reload the data
            try {
                var jasaKaryawanModal = bootstrap.Modal.getInstance(document.getElementById('jasa-karyawan-modal'));
                if (jasaKaryawanModal) {
                    jasaKaryawanModal.hide();
                } else {
                    $('#jasa-karyawan-modal').modal('hide');
                }
            } catch (e) {
                $('#jasa-karyawan-modal').modal('hide');
            }
            loadJasaKaryawanData();

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Berhasil',
                text: response.message
            });
        },
        error: function(xhr) {
            if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                let errorMessage = 'Terjadi kesalahan validasi:';
                for (const field in xhr.responseJSON.errors) {
                    errorMessage += `\n- ${xhr.responseJSON.errors[field][0]}`;
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Validasi Gagal',
                    text: errorMessage
                });
            } else {
                // Handle other errors
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menyimpan data.'
                });
            }
        },
        complete: function() {
            // Re-enable the save button
            $('#save-jasa-karyawan-btn').prop('disabled', false).text('Simpan');
        }
    });
}

// Function to editMonthly Report
function editJasaKaryawan(id) {
    // Find the item in the table
    const row = $(`button.edit-btn[data-id="${id}"]`).closest('tr');
    const date = row.find('td:eq(1)').text().split('/').reverse().join('-');
    const amount = row.find('td:eq(2)').text().replace(/[^\d]/g, '');
    const notes = row.find('td:eq(4)').text() === '-' ? '' : row.find('td:eq(4)').text();

    // Set form values
    $('#jasa-karyawan-id').val(id);
    $('#date').val(date);
    $('#amount').val(amount);
    $('#notes').val(notes);

    // Show the file preview section if there's a file
    const hasFile = row.find('td:eq(5)').find('button.view-file-btn').length > 0;
    if (hasFile) {
        $('#file-preview').removeClass('d-none');
        $('#current-file-name').text('File tersedia (tidak perlu diubah jika tidak ingin mengganti file)');
    } else {
        $('#file-preview').addClass('d-none');
    }

    // Update modal title and show the modal
    $('#jasa-karyawan-modal-label').text('Edit Monthly Report');

    // Try both Bootstrap 4 and 5 modal methods
    try {
        var jasaKaryawanModal = new bootstrap.Modal(document.getElementById('jasa-karyawan-modal'));
        jasaKaryawanModal.show();
    } catch (e) {
        $('#jasa-karyawan-modal').modal('show');
    }
}

// Function to deleteMonthly Report
function deleteJasaKaryawan(id) {
    Swal.fire({
        title: 'Konfirmasi Hapus',
        text: 'Apakah Anda yakin ingin menghapus data ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/warehouse/jasa-karyawan/${id}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    loadJasaKaryawanData();
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: response.message
                    });
                },
                error: function(xhr) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus data.'
                    });
                }
            });
        }
    });
}

// Function to view file
function viewFile(id) {
    // Find the item in the table
    const row = $(`button.view-file-btn[data-id="${id}"]`).closest('tr');
    const date = row.find('td:eq(1)').text();
    const amount = row.find('td:eq(2)').text();
    const status = row.find('td:eq(3)').find('span').text();
    const notes = row.find('td:eq(4)').text();

    // Set detail values
    $('#detail-date').text(date);
    $('#detail-amount').text(amount);
    $('#detail-status').text(status);
    $('#detail-notes').text(notes);

    // Get the file and display it
    $.ajax({
        url: `/warehouse/jasa-karyawan/data`,
        type: 'GET',
        data: {
            id: id
        },
        success: function(response) {
            const item = response.data.find(item => item.id == id);
            if (item && item.file_path) {
                const fileUrl = `/assets/jasa_karyawan/${item.file_path}`;
                const fileExt = item.file_path.split('.').pop().toLowerCase();

                // Set download button URL
                $('#download-file-btn').attr('href', fileUrl);

                // Clear previous content
                $('#file-container').empty();

                // Display file based on type
                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                    // Image file
                    $('#file-container').html(`<img src="${fileUrl}" class="img-fluid" alt="File Preview">`);
                } else if (fileExt === 'pdf') {
                    // PDF file
                    $('#file-container').html(`<iframe src="${fileUrl}" width="100%" height="500px"></iframe>`);
                } else {
                    // Other file types
                    $('#file-container').html(`
                        <div class="alert alert-info">
                            <i class="mdi mdi-file-document-outline me-2"></i>
                            File tidak dapat ditampilkan. Silakan download untuk melihat.
                        </div>
                    `);
                }

                // Show the modal
                try {
                    var viewFileModal = new bootstrap.Modal(document.getElementById('view-file-modal'));
                    viewFileModal.show();
                } catch (e) {
                    $('#view-file-modal').modal('show');
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'File tidak ditemukan.'
                });
            }
        },
        error: function(xhr) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Terjadi kesalahan saat mengambil file.'
            });
        }
    });
}

// Function to reset the form
function resetForm() {
    $('#jasa-karyawan-form')[0].reset();
    $('#jasa-karyawan-id').val('');
    $('#file-preview').addClass('d-none');
}
