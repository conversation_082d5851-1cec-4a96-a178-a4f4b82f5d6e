<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('unit_transactions', function (Blueprint $table) {
            // Change mr_date and do_date from timestamp to date to avoid timezone issues
            $table->date('mr_date')->nullable()->change();
            $table->date('do_date')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unit_transactions', function (Blueprint $table) {
            // Revert back to timestamp
            $table->timestamp('mr_date')->nullable()->change();
            $table->timestamp('do_date')->nullable()->change();
        });
    }
};
