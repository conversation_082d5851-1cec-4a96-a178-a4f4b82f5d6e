<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use App\Models\JasaKaryawan;
use App\Models\LogAktivitas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class JasaKaryawanController extends Controller
{
    /**
     * Display a listing of the resource for HRD reports.
     */
    public function indexHrd()
    {
        return view('sites.jasa_karyawan.index_hrd');
    }

    /**
     * Display a listing of the resource for Sales reports.
     */
    public function indexSales()
    {
        return view('sites.jasa_karyawan.index_sales');
    }

    /**
     * GetMonthly Reportdata for AJAX request.
     */
    public function getData(Request $request)
    {
        $siteId = session('site_id');

        $query = JasaKaryawan::with(['employee'])
            ->where('site_id', $siteId)
            ->orderBy('created_at', 'desc');

        // Apply type filter (default to 'hrd' if not provided)
        $type = $request->input('type', 'hrd');
        $query->where('type', $type);

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('notes', 'like', "%{$search}%")
                  ->orWhere('amount', 'like', "%{$search}%")
                  ->orWhere('status', 'like', "%{$search}%");
            });
        }

        // Apply date filter if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            if (!empty($startDate) && !empty($endDate)) {
                $query->whereBetween('date', [$startDate, $endDate]);
            }
        }

        // Apply status filter if provided
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Paginate the results
        $perPage = $request->input('per_page', 5);
        $jasaKaryawan = $query->paginate($perPage);

        return response()->json($jasaKaryawan);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date',
            'amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'file' => 'required|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx,xls,xlsx',
            'type' => 'nullable|in:hrd,sales',
        ], [
            'file.max' => 'Ukuran file tidak boleh lebih dari 10MB',
            'file.mimes' => 'Format file harus pdf, jpg, jpeg, png, doc, docx, xls, atau xlsx',
            'type.in' => 'Tipe harus hrd atau sales',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            // Handle file upload
            $filePath = null;
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $file->move(public_path('assets/jasa_karyawan'), $fileName);
                $filePath = $fileName;
            }

            // Create newMonthly Reportrecord
            $jasaKaryawan = new JasaKaryawan([
                'site_id' => session('site_id'),
                'employee_id' => session('employee_id'),
                'file_path' => $filePath,
                'date' => $request->date,
                'amount' => $request->amount ?? 0,
                'status' => 'submitted',
                'notes' => $request->notes ?? '',
                'type' => $request->type ?? 'hrd', // Default to 'hrd' if not provided
            ]);

            $jasaKaryawan->save();

            // Log the activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menambahkan Monthly Report',
                'description' => "User " . session('name') . " menambahkan Monthly Report, Tanggal: " . $request->date,
                'table' => "jasa_karyawan",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Jasa Karyawan berhasil ditambahkan',
                'data' => $jasaKaryawan
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creatingMonthly Report: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $jasaKaryawan = JasaKaryawan::findOrFail($id);

        // Check if theMonthly Reportbelongs to the current site
        if ($jasaKaryawan->site_id !== session('site_id')) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki akses untuk mengedit data ini'
            ], 403);
        }

        // Check if the status is 'done', if so, don't allow editing
        if ($jasaKaryawan->status === 'done') {
            return response()->json([
                'success' => false,
                'message' => 'Jasa Karyawan dengan status "done" tidak dapat diedit'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'date' => 'required|date',
            'amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'file' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx,xls,xlsx',
            'type' => 'nullable|in:hrd,sales',
        ], [
            'file.max' => 'Ukuran file tidak boleh lebih dari 10MB',
            'file.mimes' => 'Format file harus pdf, jpg, jpeg, png, doc, docx, xls, atau xlsx',
            'type.in' => 'Tipe harus hrd atau sales',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            // Handle file upload if a new file is provided
            if ($request->hasFile('file')) {
                // Delete old file if it exists
                if ($jasaKaryawan->file_path && file_exists(public_path('assets/jasa_karyawan/' . $jasaKaryawan->file_path))) {
                    unlink(public_path('assets/jasa_karyawan/' . $jasaKaryawan->file_path));
                }

                // Upload new file
                $file = $request->file('file');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $file->move(public_path('assets/jasa_karyawan'), $fileName);
                $jasaKaryawan->file_path = $fileName;
            }

            // UpdateMonthly Reportrecord
            $jasaKaryawan->date = $request->date;
            $jasaKaryawan->amount = $request->amount ?? 0;
            $jasaKaryawan->notes = $request->notes ?? '';
            // Update type if provided
            if ($request->has('type')) {
                $jasaKaryawan->type = $request->type;
            }
            $jasaKaryawan->save();

            // Log the activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Mengubah Monthly Report',
                'description' => "User " . session('name') . " mengubah Monthly Report, Tanggal: " . $request->date,
                'table' => "jasa_karyawan",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Jasa Karyawan berhasil diperbarui',
                'data' => $jasaKaryawan
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updatingMonthly Report: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $id)
    {
        $jasaKaryawan = JasaKaryawan::findOrFail($id);

        // Check if theMonthly Reportbelongs to the current site
        if ($jasaKaryawan->site_id !== session('site_id')) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki akses untuk menghapus data ini'
            ], 403);
        }

        // Check if the status is 'done', if so, don't allow deletion
        if ($jasaKaryawan->status === 'done') {
            return response()->json([
                'success' => false,
                'message' => 'Jasa Karyawan dengan status "done" tidak dapat dihapus'
            ], 403);
        }

        DB::beginTransaction();
        try {
            // Delete file if it exists
            if ($jasaKaryawan->file_path && file_exists(public_path('assets/jasa_karyawan/' . $jasaKaryawan->file_path))) {
                unlink(public_path('assets/jasa_karyawan/' . $jasaKaryawan->file_path));
            }

            // Delete the record
            $jasaKaryawan->delete();

            // Log the activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Monthly Report',
                'description' => "User " . session('name') . " menghapus Monthly Report: ID: " . $id,
                'table' => "jasa_karyawan",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Jasa Karyawan berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deletingMonthly Report: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }
}
